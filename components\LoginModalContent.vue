<template>
  <Card class="py-10">
    <!-- Show skeleton loading while authenticating -->
    <div v-if="auth.isAuthenticating">
      <SkeletonLoader type="login-modal" />
    </div>

    <!-- Show login options when not authenticating -->
    <div v-else>
      <Button
        block
        color="success"
        leading-icon="simple-icons:line"
        :loading="auth.isLoading"
        @click="handleLineLogin"
      >
        เข้าสู่ระบบด้วย LINE
      </Button>

      <div class="text-center">
        <p class="mt-2 text-xs text-gray-500">
          การใช้งานแบบไม่เข้าสู่ระบบอาจจะไม่สามารถบันทึกประวัติได้
        </p>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
const auth = useAuth()

const handleLineLogin = () => {
  auth.loginWithLine()
}
</script>
