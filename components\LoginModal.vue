<template>
  <Modal
    v-if="isDesktop"
    title="เข้าสู่ระบบ"
  >
    <template #body>
      <LoginModalContent />
    </template>
  </Modal>
  <Drawer
    v-else
    title="เข้าสู่ระบบ"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :handle="false"
    :ui="{ header: 'flex items-center justify-between' }"
  >
    <template #header>
      <h2 class="text-highlighted text-xl font-bold">
        เข้าสู่ระบบ
      </h2>

      <Button
        color="neutral"
        variant="ghost"
        icon="i-lucide-x"
        @click="emits('close', false)"
      />
    </template>
    <template #body>
      <LoginModalContent />
    </template>
  </Drawer>
</template>

<script setup lang="ts">
import { useMediaQuery } from '@vueuse/core'

const emits = defineEmits<{ close: [boolean] }>()

const isDesktop = useMediaQuery('(min-width: 768px)')
</script>
