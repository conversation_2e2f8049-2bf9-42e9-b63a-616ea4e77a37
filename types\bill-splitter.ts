export interface FoodItem {
  name: string
  price: number
  sharing: boolean[]
  sharingWeights: number[]
  paidBy: number | null
}

export interface NewItem {
  name: string
  price: number
}

export interface Transfer {
  id: number
  from: number
  to: number
  amount: number
}

export interface Balance {
  personIndex: number
  balance: number
}

export interface ShareableFoodItem {
  name: string
  price: number
  sharing: boolean[]
  sharingWeights: number[]
  paidBy: number | null
}

export interface ShareablePerson { // Added this interface
  name: string
  promptPayId: string | null
}

export interface ShareableAppState {
  people: ShareablePerson[]
  items: ShareableFoodItem[]
}

export interface AppState {
  people: Person[]
  items: FoodItem[]
}

export interface Person {
  name: string
  promptPayId: string | null // Added for PromptPay ID
}

export interface BillState extends AppState {
  id: string
  name: string
  created_at: string
  created_by_id: string
}
