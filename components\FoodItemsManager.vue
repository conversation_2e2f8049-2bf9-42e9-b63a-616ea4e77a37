<template>
  <Card>
    <div class="mb-4 flex items-start justify-between">
      <h3
        class="text-lg font-semibold"
      >
        รายการ
      </h3>
      <div class="flex items-center gap-2">
        <Button
          color="info"
          size="md"
          variant="link"
          class="border-1 border-white/20"
          icon="material-symbols:image-arrow-up-outline"
          @click="billImportModal.open({
            onSubmit: () => {
              billImportModal.close()
            },
          })"
        >
          นำเข้าจากรูปภาพบิล
        </Button>
        <Button
          icon="ph:plus-bold"
          title="เพิ่มรายการอาหาร"
          size="md"
          @click="onShowAddItemModal"
        >
          เพิ่ม
        </Button>
      </div>
    </div>

    <div
      v-if="!ArrayHelper.isEmpty(bill.item!.items)"
    >
      <div
        class="space-y-2 divide-y divide-gray-200"
      >
        <FoodItemCard
          v-for="(item, index) in bill.item?.items"
          :key="`card-${index}`"
          :item="item"
          :item-index="index"
          :people="bill.item!.people"
          @update="bill.updateItem.update(index, $event)"
          @remove="onRemoveItem(index)"
        />
      </div>
    </div>
    <div
      v-else
      class="border-primary text-primary flex cursor-pointer items-center justify-center gap-2 rounded-lg border border-dashed px-2 py-6 hover:bg-primary/10"
      @click="onShowAddItemModal"
    >
      <Icon
        name="ph:plus-bold"
        class="size-6"
      />
      <p>
        เพิ่มรายการอาหาร
      </p>
    </div>
  </Card>
</template>

<script setup lang="ts">
import FoodItemCard from './FoodItemCard.vue'
import FoodFormModal from './FoodFormModal.vue'
import type { FoodItem } from '~/types/bill-splitter'
import { BillImportModal } from '#components'

const overlay = useOverlay()
const bill = useBillById()
const noti = useNotification()
const dialog = useDialog()
const addModal = overlay.create(FoodFormModal)
const billImportModal = overlay.create(BillImportModal)

const onShowAddItemModal = () => {
  addModal.open({
    status: () => bill.addItem.status,
    onSubmit: (values: FoodItem) => {
      bill.addItem.update(values)
    },
  })
}

const onRemoveItem = (index: number) => {
  dialog.confirm({
    title: 'ยืนยันการลบรายการอาหาร',
    description: 'คุณแน่ใจหรือไม่ว่าต้องการลบรายการอาหารนี้?',
    confirmText: 'ยืนยัน',
  }).then(() => {
    bill.removeItem.update(index)
  })
}

useWatchTrue(() => bill.addItem.status.isSuccess, () => {
  addModal.close()
  noti.success({
    title: 'เพิ่มรายการอาหารสำเร็จ',
    description: 'รายการอาหารถูกเพิ่มลงในบิลเรียบร้อยแล้ว',
  })
})

useWatchTrue(() => bill.updateItem.status.isSuccess, () => {
  noti.success({
    title: 'อัปเดตรายการอาหารสำเร็จ',
    description: 'รายการอาหารถูกอัปเดตเรียบร้อยแล้ว',
  })
})

useWatchTrue(() => bill.removeItem.status.isSuccess, () => {
  noti.success({
    title: 'ลบรายการอาหารสำเร็จ',
    description: 'รายการอาหารถูกลบเรียบร้อยแล้ว',
  })
})
</script>
