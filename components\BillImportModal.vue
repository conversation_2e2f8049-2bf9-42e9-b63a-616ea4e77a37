<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="นำเข้ารายการจากรูปภาพบิล"
  >
    <template #body>
      <form @submit="handleSubmitForm">
        <div
          class="flex cursor-pointer flex-col items-center gap-2 rounded-lg border-2 border-dashed border-gray-300 p-4 text-center transition hover:border-primary hover:bg-primary/10"
          @click="handleImageImport"
        >
          <label
            v-if="!form.values.file"
            class="flex h-full w-full cursor-pointer flex-col items-center gap-2"
          >
            <Icon
              name="tdesign:bill"
              class="text-primary mb-2 h-10 w-10"
            />
            <span class="font-medium">คลิกเพื่อเลือกรูปภาพบิล</span>
          </label>
          <div
            v-else
            class="flex flex-col items-center gap-2"
          >
            <img
              :src="getImageUrl()"
              class="max-h-64 max-w-full rounded"
            />
            <p class="mt-3 font-bold">
              ไฟล์ที่เลือก
            </p>
            <p
              class="max-w-50 truncate font-medium text-gray-500"
              :title="form.values.file.name"
            >
              {{ form.values.file.name }}
            </p>
          </div>
        </div>
        <p
          v-if="form.errors.value.file"
          class="text-error mt-2 text-sm"
        >
          {{ form.errors.value.file }}
        </p>
        <div>
          <label
            class="mt-4 mb-1 block text-sm font-bold"
          >
            จ่ายโดย
          </label>
          <Popover
            v-model:open="isOpenPeoplePaid"
            :content="{
              side: 'bottom',
              align: 'start',
            }"
          >
            <div>
              <PersonAvatar
                v-if="form.values.paidBy !== null && form.values.paidBy !== undefined"
                :person="bill.item!.people[form.values.paidBy - 1]"
                action="edit"
              />
              <PersonAdd
                v-else
              />
            </div>

            <template #content>
              <CommandPalette
                :model-value="{
                  value: form.values.paidBy,
                  label: bill.item!.people[form.values.paidBy - 1]?.name,
                  avatar: {
                    src: getAvatarUrl(bill.item!.people[form.values.paidBy - 1]?.name),
                  },
                }"
                placeholder="เลือกคนจ่าย"
                :groups="[{
                  id: 'select-person',
                  items: bill.item!.people.map((p, index) => ({
                    label: p.name,
                    value: index + 1,
                    avatar: {
                      src: getAvatarUrl(p.name),
                    },
                  })),
                }]"
                :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                @update:model-value="(value) => {
                  form.setFieldValue('paidBy', value?.value ?? null)
                  isOpenPeoplePaid = false
                }"
              />
            </template>
          </Popover>
        </div>
        <div>
          <div class="mt-4 mb-1 flex items-center justify-between">
            <label
              class="block text-sm font-bold"
            >
              แชร์กับ
            </label>
            <Button
              v-if="(form.values.sharing?.length || 0) !== bill.item!.people.length"
              color="info"
              variant="ghost"
              size="lg"
              icon="ph:lightning-light"
              @click="form.setFieldValue('sharing', bill.item!.people.map((_, index) => index + 1))"
            >
              แชร์กับทุกคน
            </Button>
          </div>
          <div class="flex flex-wrap items-start gap-2">
            <PersonAvatar
              v-for="(person, personIndex) in form.values.sharing || []"
              :key="personIndex"
              :person="bill.item!.people[person - 1]"
              action="delete"
              @click="form.setFieldValue(
                'sharing',
                form.values.sharing?.filter((_ : any, index : number) => index !== personIndex))"
            />
            <Popover
              v-if="(form.values.sharing?.length || 0) < bill.item!.people.length"
              :content="{
                side: 'bottom',
                align: 'start',
              }"
            >
              <PersonAdd />

              <template #content>
                <CommandPalette
                  placeholder="เลือกคนที่แชร์"
                  :groups="[{
                    id: 'select-person',
                    items: bill.item!.people.map((p, index) => ({
                      label: p.name,
                      value: index + 1,
                      avatar: {
                        src: getAvatarUrl(p.name),
                      },
                    })).filter((person) => !form.values.sharing?.includes(person.value)),
                  }]"
                  :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                  @update:model-value="(value) => {
                    form.setFieldValue('sharing', [...form.values.sharing || [], value.value])
                  }"
                />
              </template>
            </Popover>
          </div>
        </div>
        <Submit />
      </form>
    </template>
    <template #footer>
      <div class="flex flex-1 justify-end gap-3">
        <Button
          block
          :loading="ocr.status.value.isLoading || bill.addItem.status.isLoading"
          :disabled="!form.meta.value.dirty"
          @click="handleSubmitForm"
        >
          {{
            (ocr.status.value.isLoading || bill.addItem.status.isLoading)
              ? 'อาจใช้เวลาสักครู่...'
              : 'นำเข้า'
          }}
        </Button>
      </div>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { useFileDialog, useMediaQuery } from '@vueuse/core'
import { Modal, Slideover } from '#components'
import { useOCR } from '~/loaders/useOCR'
import { getAvatarUrl } from '~/utils/avatar'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  onSubmit: () => void
}>()

const isDesktop = useMediaQuery('(min-width: 768px)')
const bill = useBillById()
const noti = useNotification()
const ocr = useOCR()
const isOpenPeoplePaid = ref(false)

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      file: v.optional(v.pipe(
        v.file('โปรดเลือกรูปภาพบิล'),
        v.minSize(1, 'โปรดเลือกรูปภาพบิล'),
      )),
      paidBy: v.nullish(v.pipe(v.number()), null),
      sharing: v.optional(v.array(v.number()), []),
    }),
  ),
})

const getImageUrl = (): string => {
  return URL.createObjectURL(form.values?.file as File)
}

const file = useFileDialog({
  accept: 'image/*',
  directory: false,
})

const handleImageImport = (): void => {
  file.open()
}

const processImage = (imageFile: File) => {
  const MAX_WIDTH = 1024
  const MAX_HEIGHT = 1024

  const image = new Image()
  const reader = new FileReader()

  reader.onload = (e) => {
    if (typeof e.target?.result !== 'string') return
    image.src = e.target.result

    image.onload = () => {
      let {
        naturalWidth: width, naturalHeight: height,
      } = image

      if (width > MAX_WIDTH || height > MAX_HEIGHT) {
        if (width / height > MAX_WIDTH / MAX_HEIGHT) {
          if (width > MAX_WIDTH) {
            height = Math.round((height * MAX_WIDTH) / width)
            width = MAX_WIDTH
          }
        } else {
          if (height > MAX_HEIGHT) {
            width = Math.round((width * MAX_HEIGHT) / height)
            height = MAX_HEIGHT
          }
        }

        const canvas = document.createElement('canvas')

        canvas.width = width
        canvas.height = height
        const ctx = canvas.getContext('2d')
        if (!ctx) return

        ctx.drawImage(image, 0, 0, width, height)

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], imageFile.name, {
                type: imageFile.type,
                lastModified: Date.now(),
              })

              ocr.run({
                data: {
                  image: resizedFile,
                },
              })
            } else {
              // Handle error if blob creation fails, perhaps use original file
              ocr.run({
                data: {
                  image: imageFile,
                },
              })
            }
          },
          imageFile.type,
          0.9, // image quality
        )
      } else {
        // Image doesn't need resizing
        ocr.run({
          data: {
            image: imageFile,
          },
        })
      }
    }

    image.onerror = () => {
      // Handle image load error
      noti.error({
        title: 'ไม่สามารถโหลดรูปภาพ',
        description: 'ไม่สามารถโหลดรูปภาพที่เลือกได้',
      })

      file.reset()
    }
  }

  reader.readAsDataURL(imageFile)
}

file.onChange(async (files) => {
  if (!files?.length) return
  form.setFieldValue('file', files[0])
})

useWatchTrue(() => ocr.status.value.isSuccess, () => {
  const ocrData = ocr.data.value
  if (!ocrData?.items) return

  bill.addItem.update(ocrData.items.map((item: any) => ({
    ...item,
    paidBy: form.values.paidBy ? form.values.paidBy - 1 : null,
    sharing: bill.item!.people.map((_, index) => !!form.values.sharing?.includes(index + 1)),
  })))
})

useWatchTrue(() => bill.addItem.status.isSuccess, () => {
  file.reset()
  props.onSubmit()
})

useWatchTrue(() => ocr.status.value.isError, () => {
  file.reset()
  noti.error({
    title: 'นำเข้าข้อมูลล้มเหลว',
    description: 'ไม่สามารถนำเข้าข้อมูลจากรูปภาพได้ กรุณาลองใหม่อีกครั้ง',
  })
})

const handleSubmitForm = form.handleSubmit((values: any) => {
  processImage(values.file as File)
})
</script>
