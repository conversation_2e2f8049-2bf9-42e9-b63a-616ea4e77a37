import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://ylagwfynqstzwpzxqsfe.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlsYWd3ZnlucXN0endwenhxc2ZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwNTYxNTUsImV4cCI6MjA2NjYzMjE1NX0.XKbIGZ4SbcqKyYlYNw5HEaf1yQstkcqxhqzEtfg8XZ0'

export const useSupabaseClient = () => {
  const supabase = createClient(supabaseUrl, supabaseAnonKey)

  return supabase
}

export interface IFetchOptions {
  filters?: [
    {
      column: string
      operator: string
      value: any
    },
  ]
  sort?: { column: string
    ascending: boolean }
}

export const useAPIList = <T>(options: {
  endpoint: string
  select?: string
  or?: string
  eq?: Record<string, any>
  filters?: Array<{
    column: string
    operator: string
    value: any
  }>
  sort?: {
    column: string
    ascending: boolean
  }
}) => {
  const supabase = useSupabaseClient()
  const items = ref<T[]>([])
  const status = ref<IStatus>(ObjectHelper.createStatus())

  const fetch = async (fetchOptions = {} as IFetchOptions) => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
    const query = supabase.from(options.endpoint).select(options.select ?? '*')

    const mergedFilterOptions = [...(options.filters || []), ...(fetchOptions.filters || [])]

    for (const filter of mergedFilterOptions) {
      query.filter(filter.column, filter.operator, filter.value)
    }

    if (options.or) {
      query.or(options.or)
    }

    if (options.eq) {
      for (const [column, value] of Object.entries(options.eq)) {
        query.eq(column, value)
      }
    }

    if (options.sort) {
      query.order(options.sort.column, {
        ascending: options.sort.ascending,
      })
    }

    const res = await query

    if (res.error) {
      status.value = ObjectHelper.toErrorStatus(status.value, res.error)
    } else {
      items.value = res.data as T[]
      status.value = ObjectHelper.toSuccessStatus(status.value)
    }

    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  const setFetchLoading = () => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
  }

  return {
    items,
    status,
    fetch,
    setFetchLoading,
  }
}

export const useAPICreate = <T>(options: {
  endpoint: string
  select?: string
}) => {
  const supabase = useSupabaseClient()
  const status = ref<IStatus>(ObjectHelper.createStatus())
  const item = ref<T | null>(null)

  const create = async (data: any) => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
    const res = await supabase.from(options.endpoint).insert(data).select(options.select ?? '*').single()

    if (res.error) {
      status.value = ObjectHelper.toErrorStatus(status.value, res.error)
    } else {
      item.value = res.data
      status.value = ObjectHelper.toSuccessStatus(status.value)
    }

    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  return {
    status,
    create,
    item,
  }
}

export const useAPIUpdate = <T>(options: {
  endpoint: string
  select?: string
}) => {
  const supabase = useSupabaseClient()
  const status = ref<IStatus>(ObjectHelper.createStatus())
  const item = ref<T | null>(null)

  const update = async (id: string, data: any) => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
    const res = await supabase
      .from(options.endpoint)
      .update(data as never)
      .eq('id', id)
      .select(options.select ?? '*')
      .single()

    if (res.error) {
      status.value = ObjectHelper.toErrorStatus(status.value, res.error)
    } else {
      item.value = res.data as T
      status.value = ObjectHelper.toSuccessStatus(status.value)
    }

    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  return {
    status,
    update,
    item,
  }
}

export const useAPIRemove = (options: { endpoint: string }) => {
  const supabase = useSupabaseClient()
  const status = ref<IStatus>(ObjectHelper.createStatus())

  const remove = async (id: string) => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
    const res = await supabase.from(options.endpoint).delete().eq('id', id)

    if (res.error) {
      status.value = ObjectHelper.toErrorStatus(status.value, res.error)
    } else {
      status.value = ObjectHelper.toSuccessStatus(status.value)
    }

    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  return {
    status,
    remove,
  }
}

export const useAPIFind = <T>(options: {
  endpoint: string
  select?: string
  filters?: [
    {
      column: string
      operator: string
      value: any
    },
  ]
}) => {
  const supabase = useSupabaseClient()
  const item = ref<T | null>(null)
  const status = ref<IStatus>(ObjectHelper.createStatus())

  const find = async (id: string) => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
    const query = supabase.from(options.endpoint).select(options.select ?? '*').eq('id', id)

    const mergedFilterOptions = options.filters || []

    for (const filter of mergedFilterOptions) {
      query.filter(filter.column, filter.operator, filter.value)
    }

    const res = await query.single()

    if (res.error) {
      status.value = ObjectHelper.toErrorStatus(status.value, res.error)
    } else {
      item.value = res.data as T
      status.value = ObjectHelper.toSuccessStatus(status.value)
    }

    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  const setLoading = () => {
    status.value = ObjectHelper.toLoadingStatus(status.value)
  }

  const setItem = (data: T | null) => {
    item.value = data
    status.value = ObjectHelper.toSuccessStatus(status.value)
    status.value = ObjectHelper.toCompleteStatus(status.value)
  }

  return {
    item,
    status,
    find,
    setLoading,
    setItem,
  }
}
