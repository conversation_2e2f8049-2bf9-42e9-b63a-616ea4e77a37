<template>
  <div class="pb-2">
    <div class="mb-2 flex items-start justify-between gap-4">
      <div class="flex w-full items-start justify-between gap-2">
        <div
          class="font-semibold"
        >
          {{ item.name }}
        </div>

        <div
          class="text-right font-bold whitespace-nowrap"
        >
          {{ NumberHelper.toCurrency(item.price) }} ฿
        </div>
      </div>
      <div class="flex items-center gap-2">
        <DropdownMenu
          :items="[
            [
              {
                label: 'แก้ไข',
                icon: 'i-lucide-pencil',
                onSelect: () => {
                  onShowEditItemModal()
                },
              },
              {
                label: 'ลบ',
                color: 'error',
                icon: 'i-lucide-trash',
                onSelect: () => {
                  $emit('remove')
                },
              },
            ],
          ]"
          :ui="{ content: 'w-48' }"
          :content="{
            align: 'end',
            side: 'bottom',
          }"
        >
          <Button
            color="neutral"
            variant="ghost"
            icon="ph:dots-three-vertical-bold"
            class="px-0 py-0"
          />
        </DropdownMenu>
      </div>
    </div>

    <!-- Body -->
    <div>
      <div class="flex justify-between gap-8">
        <!-- Payer Selection -->
        <div class="flex flex-col items-start justify-start">
          <div class="mb-2 flex items-center gap-1 text-sm font-semibold text-gray-600">
            จ่ายโดย
          </div>
          <Popover
            v-model:open="isOpenPeoplePaid"
            :content="{
              side: 'right',
              align: 'start',
            }"
          >
            <div>
              <PersonAvatar
                v-if="item.paidBy !== null && item.paidBy !== undefined"
                :person="bill.item!.people[item.paidBy]"
                action="edit"
                size="md"
              />
              <PersonAdd
                v-else
                size="md"
              />
            </div>

            <template #content>
              <CommandPalette
                :model-value="{
                  value: item.paidBy,
                  label: bill.item!.people[item.paidBy]?.name,
                  avatar: {
                    src: getAvatarUrl(bill.item!.people[item.paidBy]?.name),
                  },
                }"
                placeholder="เลือกคนจ่าย"
                :groups="[{
                  id: 'select-person',
                  items: bill.item!.people.map((p, index) => ({
                    label: p.name,
                    value: index,
                    avatar: {
                      src: getAvatarUrl(p.name),
                    },
                  })),
                }]"
                :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                @update:model-value="(value) => {
                  emits('update', {
                    ...props.item,
                    paidBy: value?.value ?? null,
                  })
                  isOpenPeoplePaid = false
                }"
              />
            </template>
          </Popover>
        </div>

        <!-- Sharing Selection -->
        <div class="flex flex-2 flex-col items-end pr-9">
          <div class="mb-2 flex items-start justify-between gap-4 text-sm font-semibold text-gray-600">
            <p
              v-if="hasAnySharing && !isWeightMode"
              class="text-info cursor-pointer"
              title="ปรับน้ำหนักการแบ่งจ่าย"
              @click="toggleWeightMode"
            >
              <Icon name="hugeicons:balance-scale" /> ปรับน้ำหนัก
            </p>
            <p>แชร์กับ</p>
          </div>
          <div class="flex flex-row-reverse flex-wrap justify-start gap-2">
            <template
              v-for="(person, personIndex) in bill.item!.people"
              :key="person.name"
            >
              <div
                v-if="item.sharing[personIndex]"
                class="flex flex-col items-center"
              >
                <PersonAvatar
                  :person="person"
                  action="delete"
                  size="md"
                  :weight="item.sharingWeights?.[personIndex]"
                  @click="onRemovePersonSharing(personIndex)"
                />
                <div
                  v-if="item.sharing[personIndex] && isWeightMode"
                  class="mt-1.5 flex flex-col items-center"
                >
                  <div class="flex items-center">
                    <button
                      class="rounded-l border border-gray-300 px-1.5 py-0.5 text-xs transition-colors hover:bg-gray-100 focus:outline-none"
                      @click="decrementWeight(personIndex)"
                    >
                      -
                    </button>
                    <input
                      :value="item.sharingWeights[personIndex]"
                      type="number"
                      min="0.1"
                      step="0.1"
                      class="w-12 border-t border-b border-gray-300 px-1 py-0.5 text-center text-xs [appearance:textfield] focus:border-indigo-500 focus:outline-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                      @input="updateWeight(personIndex, $event)"
                    />
                    <button
                      class="rounded-r border border-gray-300 px-1.5 py-0.5 text-xs transition-colors hover:bg-gray-100 focus:outline-none"
                      @click="incrementWeight(personIndex)"
                    >
                      +
                    </button>
                  </div>
                </div>
              </div>
            </template>
            <Popover
              v-if="(!isWeightMode && sharingCount < bill.item!.people.length) || bill.item!.people.length === 0"
              :content="{
                side: 'top',
              }"
            >
              <PersonAdd
                size="md"
              />

              <template #content>
                <Button
                  variant="link"
                  icon="ph:lightning-light"
                  block
                  class="justify-start"
                  color="info"
                  @click="() => {
                    const isNewItem = !item.sharingWeights || item.sharingWeights.length !== bill.item!.people.length
                    const newSharing = bill.item!.people.map(() => true)
                    let newSharingWeights
                    if (isNewItem) {
                      newSharingWeights = bill.item!.people.map(() => 1)
                    }
                    else {
                      // Keep existing sharingWeights if not 1 or 0, set to 1 if 0 or 1
                      newSharingWeights = bill.item!.people.map((_, idx) => {
                        const w = item.sharingWeights[idx]
                        return w && w !== 1 ? w : 1
                      })
                    }
                    emits('update', {
                      ...item,
                      sharing: newSharing,
                      sharingWeights: newSharingWeights,
                    })
                  }"
                >
                  แชร์กับทุกคน
                </Button>
                <CommandPalette
                  placeholder="เลือกคนที่แชร์"
                  :groups="[{
                    id: 'select-person',
                    items: bill.item!.people.map((p, index) => ({
                      label: p.name,
                      value: index,
                      avatar: {
                        src: getAvatarUrl(p.name),
                      },
                    })).filter((person) => !item.sharing[person.value]),
                  }]"
                  :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                  @update:model-value="(value) => {
                    const newSharing = [...item.sharing]
                    newSharing[value.value] = true
                    // Set sharingWeights: keep existing if >0, else set to 1
                    let newSharingWeights = item.sharingWeights ? [...item.sharingWeights] : []
                    // Ensure length matches people
                    while (newSharingWeights.length < bill.item!.people.length) {
                      newSharingWeights.push(1)
                    }
                    newSharingWeights = newSharing.map((share, idx) => share
                      ? (item.sharingWeights?.[idx] > 0 ? item.sharingWeights[idx] : 1)
                      : 0)
                    emits('update', {
                      ...item,
                      sharing: newSharing,
                      sharingWeights: newSharingWeights,
                    })
                  }"
                />
              </template>
            </Popover>
          </div>
          <Button
            v-if="isWeightMode"
            class="mt-4 rounded-full"
            size="sm"
            title="ปิดโหมดปรับน้ำหนัก"
            icon="ph:check-bold"
            color="success"
            @click="toggleWeightMode"
          >
            เสร็จ
          </Button>
        </div>
      </div>
    </div>

    <Collapsible class="mt-2">
      <div class="text-primary group flex w-full cursor-pointer items-center justify-between rounded-lg">
        <p class="text-sm">
          รายละเอียด
        </p>
        <Icon
          name="ph:arrow-circle-down-light"
          class="size-6 transition-transform duration-200 group-data-[state=open]:rotate-180"
        />
      </div>

      <template #content>
        <div class="mt-2 mb-2 space-y-4 rounded-lg border border-gray-100 p-3">
          <div class="flex items-center justify-between rounded-lg bg-gray-100 p-3 text-sm">
            <div class="font-bold">
              เฉลี่ย: {{ NumberHelper.toCurrency(averagePrice) }} ฿ / คน
            </div>
            <div class="font-bold">
              {{ sharingCount }} คน
            </div>
          </div>

          <!-- Weighted Price Details -->
          <div
            v-if="hasWeightedSharing"
            class="rounded-lg bg-gray-100 p-3"
          >
            <p class="mb-1 font-bold">
              รายละเอียดการแบ่งตามน้ำหนัก:
            </p>
            <ul class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div
                v-for="idx in sharingIndices"
                :key="idx"
                class="flex items-start justify-between rounded-lg bg-white p-3"
              >
                <div
                  class="group flex cursor-pointer flex-col"
                >
                  <PersonAvatar
                    :person="bill.item!.people[idx]"
                    size="md"
                    :weight="item.sharingWeights?.[idx]"
                  />
                </div>
                <p class="font-bold">
                  {{ NumberHelper.toCurrency(calculatePriceForPerson(idx)) }} ฿
                </p>
              </div>
            </ul>
          </div>
        </div>
      </template>
    </Collapsible>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue' // Added watch
import FoodFormModal from './FoodFormModal.vue'
import type { FoodItem } from '~/types/bill-splitter' // Import Person
import { getAvatarUrl } from '~/utils/avatar'

const emits = defineEmits<{
  remove: []
  update: [FoodItem]
}>()

const props = defineProps<{
  item: FoodItem
  itemIndex: number
}>()

const overlay = useOverlay()
const bill = useBillById()
const noti = useNotification()
const editModal = overlay.create(FoodFormModal)
const isOpenPeoplePaid = ref(false)

const onShowEditItemModal = () => {
  editModal.open({
    isEditing: true,
    values: props.item,
    status: () => bill.updateItem.status,
    onSubmit: (values: FoodItem) => {
      emits('update', values)
      editModal.close()
    },
  })
}

const onRemovePersonSharing = (personIndex: number) => {
  emits('update', {
    ...props.item,
    sharing: props.item.sharing.map((s, idx) => (idx === personIndex ? false : s)),
  })
}

const isWeightMode = ref(false)

// === Computed ===
const sharingCount = computed(() =>
  props.item.sharing.filter((share) => share).length,
)

const hasAnySharing = computed(() =>
  props.item.sharing.some((share) => share),
)

const hasWeightedSharing = computed(() =>
  props.item.sharing.some((s, i) => s && props.item.sharingWeights[i] !== 1),
)

const sharingIndices = computed(() =>
  props.item.sharing
    .map((isSharing, idx) => isSharing ? idx : -1)
    .filter((idx) => idx !== -1),
)

const averagePrice = computed(() => {
  if (sharingCount.value === 0) return 0

  if (hasWeightedSharing.value) {
    const totalWeight = props.item.sharing.reduce((sum, isSharing, idx) =>
      isSharing ? sum + props.item.sharingWeights[idx] : sum, 0,
    )

    return totalWeight > 0 ? props.item.price / sharingCount.value : 0
  }

  return props.item.price / sharingCount.value
})

// === Methods for Weight Adjustment ===
const toggleWeightMode = () => {
  isWeightMode.value = !isWeightMode.value
}

const updateWeight = (personIndex: number, event: Event) => {
  const inputElement = event.target as HTMLInputElement
  let newWeight = Number.parseFloat(inputElement.value)

  if (Number.isNaN(newWeight) || newWeight <= 0) {
    newWeight = 0.1 // Default to 0.1 if input is invalid or less than or equal to 0
  }

  // Round to one decimal place
  newWeight = Math.round(newWeight * 10) / 10

  const newSharingWeights = [...props.item.sharingWeights]

  newSharingWeights[personIndex] = newWeight

  emits('update', {
    ...props.item,
    sharingWeights: newSharingWeights,
  })
}

const incrementWeight = (personIndex: number) => {
  let currentWeight = props.item.sharingWeights[personIndex]

  if (currentWeight === undefined || currentWeight === null) {
    currentWeight = 1
  } else {
    currentWeight = Math.round((currentWeight + 0.1) * 10) / 10
  }

  const newSharingWeights = [...props.item.sharingWeights]

  newSharingWeights[personIndex] = currentWeight
  emits('update', {
    ...props.item,
    sharingWeights: newSharingWeights,
  })
}

const decrementWeight = (personIndex: number) => {
  let currentWeight = props.item.sharingWeights[personIndex]

  if (currentWeight === undefined || currentWeight === null) {
    currentWeight = 1
  } else {
    currentWeight = Math.round(Math.max(0.1, currentWeight - 0.1) * 10) / 10
  }

  const newSharingWeights = [...props.item.sharingWeights]

  newSharingWeights[personIndex] = currentWeight
  emits('update', {
    ...props.item,
    sharingWeights: newSharingWeights,
  })
}

const calculatePriceForPerson = (personIndex: number): number => {
  const totalWeight = props.item.sharing.reduce((sum, isSharing, idx) =>
    isSharing ? sum + props.item.sharingWeights[idx] : sum, 0,
  )

  if (totalWeight === 0) return 0

  const pricePerWeight = props.item.price / totalWeight

  return pricePerWeight * props.item.sharingWeights[personIndex]
}
</script>
