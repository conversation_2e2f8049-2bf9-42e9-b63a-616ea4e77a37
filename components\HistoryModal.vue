<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="บิลทั้งหมด"
  >
    <template #body>
      <HistoryList
        force-reload
        @select="emits('close', false)"
      />
    </template>
  </component>
</template>

<script lang="ts" setup>
import { useMediaQuery } from '@vueuse/core'
import { Modal, Slideover } from '#components'

const emits = defineEmits<{ close: [boolean] }>()
const isDesktop = useMediaQuery('(min-width: 768px)')
</script>
