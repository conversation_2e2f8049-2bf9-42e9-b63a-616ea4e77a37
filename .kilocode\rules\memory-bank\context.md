# Current Context

## Project Status
PaySync is a fully functional bill splitting application in production. The core features are implemented and working, including bill creation, people management, item assignment, weighted sharing, and payment calculations.

## Current Focus
The application is in a stable state with all major features implemented. Recent work has focused on:
- Authentication system with LINE login and guest sessions
- Real-time collaboration using Supabase realtime
- Session synchronization for LINE users
- Mobile-first responsive design
- OCR-based bill import functionality

## Recent Changes
- Enhanced authentication system supporting both guest and LINE users
- Implemented session mapping for LINE users to maintain data continuity
- Added real-time collaboration features using Supabase channels
- Improved mobile responsiveness and user experience
- Added sharing functionality with Web Share API and clipboard fallback

## Next Steps
- Monitor user feedback and usage patterns
- Optimize performance for mobile devices
- Consider additional payment integrations beyond PromptPay
- Enhance OCR accuracy for bill import
- Add more detailed analytics and user insights

## Technical Debt
- Some components could benefit from better TypeScript typing
- Test coverage could be improved
- Error handling could be more comprehensive
- Consider implementing offline functionality for better mobile experience

## Known Issues
- OCR import functionality may need refinement for different bill formats
- Session synchronization edge cases may need handling
- Mobile browser compatibility for certain features
