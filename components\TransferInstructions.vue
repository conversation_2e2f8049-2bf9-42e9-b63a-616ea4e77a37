<template>
  <Card
    title="การโอนเงินชดใช้"
  >
    <div v-if="bill.shouldShowTransfers">
      <div class="space-y-2">
        <div
          v-for="transfer in bill.transferCalculations"
          :key="transfer.id"
          class="bg-error-100 rounded-2xl p-3"
        >
          <div class="flex flex-row items-center justify-between gap-1 text-left text-xs md:gap-2 md:text-sm">
            <div class="flex items-center gap-2">
              <PersonAvatar
                :person="bill.item!.people[transfer.from]"
                size="sm"
              />
              <Icon
                name="ph:arrow-right"
                class="text-lg font-bold md:text-xl"
              />
              <PersonAvatar
                :person="bill.item!.people[transfer.to]"
                size="sm"
              />
            </div>

            <div class="text-error-700 flex flex-col items-end gap-1 text-sm font-bold">
              {{ NumberHelper.toCurrency(transfer.amount) }} ฿
              <Button
                size="xs"
                color="warning"
                leading-icon="ph:money-light"
                @click="showPromptPayModal(transfer)"
              >
                โอนเงิน
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-center gap-2 rounded-lg border border-dashed border-gray-400 px-2 py-6">
        <p>ยังไม่มีรายการ</p>
      </div>
    </div>
  </card>
</template>

<script setup lang="ts">
import PaymentModal from './PaymentModal.vue'
import type { Transfer } from '~/types/bill-splitter' // Import Person type

const bill = useBillById()
const overlay = useOverlay()
const paymentModal = overlay.create(PaymentModal)

const showPromptPayModal = async (transfer: Transfer) => {
  const recipient = bill.item!.people[transfer.to]
  const recipientPromptPayId = recipient.promptPayId // Get PromptPay ID from Person object

  paymentModal.open({
    recipient: bill.item!.people[transfer.to],
    transfer,
    promptPayId: recipientPromptPayId || undefined,
  })
}
</script>
