<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขรายการอาหาร' : 'เพิ่มรายการอาหาร'"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div>
          <label
            class="mt-4 mb-1 block text-sm font-bold"
          >
            จ่ายโดย
          </label>
          <Popover
            v-model:open="isOpenPeoplePaid"
            :content="{
              side: 'bottom',
              align: 'start',
            }"
          >
            <div>
              <PersonAvatar
                v-if="form.values.paidBy !== null && form.values.paidBy !== undefined"
                :person="bill.item!.people[form.values.paidBy - 1]"
                action="edit"
              />
              <PersonAdd
                v-else
              />
            </div>

            <template #content>
              <CommandPalette
                :model-value="{
                  value: form.values.paidBy,
                  label: bill.item!.people[form.values.paidBy - 1]?.name,
                  avatar: {
                    src: getAvatarUrl(bill.item!.people[form.values.paidBy - 1]?.name),
                  },
                }"
                placeholder="เลือกคนจ่าย"
                :groups="[{
                  id: 'select-person',
                  items: bill.item!.people.map((p, index) => ({
                    label: p.name,
                    value: index + 1,
                    avatar: {
                      src: getAvatarUrl(p.name),
                    },
                  })),
                }]"
                :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                @update:model-value="(value) => {
                  form.setFieldValue('paidBy', value?.value ?? null)
                  isOpenPeoplePaid = false
                }"
              />
            </template>
          </Popover>
        </div>
        <div>
          <div class="mt-4 mb-1 flex items-center justify-between">
            <label
              class="block text-sm font-bold"
            >
              แชร์กับ
            </label>
            <Button
              v-if="(form.values.sharing?.length || 0) !== bill.item!.people.length"
              color="info"
              variant="ghost"
              size="lg"
              icon="ph:lightning-light"
              @click="form.setFieldValue('sharing', bill.item!.people.map((_, index) => index + 1))"
            >
              แชร์กับทุกคน
            </Button>
          </div>

          <div class="flex flex-wrap items-start gap-2">
            <PersonAvatar
              v-for="(person, personIndex) in form.values.sharing || []"
              :key="personIndex"
              :person="bill.item!.people[person - 1]"
              action="delete"
              @click="form.setFieldValue(
                'sharing',
                form.values.sharing.filter((_ : any, index : number) => index !== personIndex))"
            />
            <Popover
              v-if="(form.values.sharing?.length || 0) < bill.item!.people.length"
              :content="{
                side: 'bottom',
                align: 'start',
              }"
            >
              <PersonAdd />

              <template #content>
                <CommandPalette
                  placeholder="เลือกคนที่แชร์"
                  :groups="[{
                    id: 'select-person',
                    items: bill.item!.people.map((p, index) => ({
                      label: p.name,
                      value: index + 1,
                      avatar: {
                        src: getAvatarUrl(p.name),
                      },
                    })).filter((person) => !form.values.sharing?.includes(person.value)),
                  }]"
                  :ui="{ input: '[&>input]:h-8 [&>input]:text-sm' }"
                  @update:model-value="(value) => {
                    form.setFieldValue('sharing', [...form.values.sharing || [], value.value])
                  }"
                />
              </template>
            </Popover>
          </div>
        </div>
        <Submit />
      </form>
    </template>
    <template #footer>
      <div class="flex flex-1 justify-end gap-3">
        <Button
          block
          :disabled="!form.meta.value.dirty"
          :loading="status().isLoading"
          @click="onSubmit"
        >
          {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
        </Button>
      </div>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { useMediaQuery } from '@vueuse/core'
import { Modal, Slideover } from '#components'
import type { FoodItem } from '~/types/bill-splitter'
import { getAvatarUrl } from '~/utils/avatar'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: any
  status: () => IStatus
  onSubmit: (values: FoodItem) => void
}>()

const bill = useBillById()
const isDesktop = useMediaQuery('(min-width: 768px)')
const isOpenPeoplePaid = ref(false)

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      price: v.pipe(v.number()),
      paidBy: v.nullish(v.pipe(v.number()), null),
      sharing: v.optional(v.array(v.number()), []),
      sharingWeights: v.optional(v.array(v.pipe(v.number())),
      ),
    }),
  ),
  initialValues: props.values && {
    ...props.values,
    paidBy: props.values?.paidBy !== null ? props.values.paidBy + 1 : null,
    sharing: props.values?.sharing
      ? props.values.sharing.map((isSharing: boolean, index: number) => (isSharing ? index + 1 : null))
        .filter((v: any) => v !== null)
      : [],
  },
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'ชื่อ',
      placeholder: 'ชื่อรายการอาหาร (เช่น บุฟเฟ่ต์, เพราเนื้อ)',
      required: true,
      autoFocus: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      name: 'price',
      label: 'ราคา',
      placeholder: 'ราคา (เช่น 199.99)',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values: any) => {
  props.onSubmit({
    ...values,
    paidBy: values.paidBy ? values.paidBy - 1 : null,
    sharing: bill.item!.people.map((_, index) => !!values.sharing?.includes(index + 1)),
  })
})
</script>
