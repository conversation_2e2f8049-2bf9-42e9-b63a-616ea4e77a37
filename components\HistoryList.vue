<template>
  <Loader :loading="billList.status.isLoading">
    <div
      v-if="!ArrayHelper.isEmpty(billList.items)"
      class="flex flex-col gap-2"
    >
      <NuxtLink
        v-for="bill in billList.items"
        :key="bill.id"
        :to="`/bills/${bill.id}`"
        class="flex cursor-pointer items-start gap-2 rounded-lg border border-gray-300 p-4 hover:bg-primary/10 hover:border-primary-300"
        @click.stop="$emit('select', bill)"
      >
        <div class="flex flex-1 flex-col">
          <div class="flex items-start justify-between">
            <div class="flex flex-col items-start gap-2 font-bold lg:flex-row lg:items-center">
              {{ bill.name }} <Badge
                v-if="sessionId !== bill.created_by_id"
                color="neutral"
                variant="outline"
                size="sm"
              >
                ผู้เยี่ยมชม
              </Badge>
            </div>

            <div>
              <p class="text-right font-bold whitespace-nowrap">
                {{ NumberHelper.toCurrency(getTotalAmount(bill.items)) }} ฿
              </p>
            </div>
          </div>
          <div class="flex flex-col gap-2 lg:flex-row lg:items-end lg:justify-between">
            <AvatarGroup
              v-if="bill.people.length > 0"
              class="mt-2"
            >
              <Avatar
                v-for="(person, index) in bill.people"
                :key="index"
                :src="getAvatarUrl(person.name)"
                alt="Benjamin Canac"
              />
            </AvatarGroup>
            <div class="flex flex-col gap-1 lg:items-end">
              <p class="text-xs text-neutral-400">อาหารทั้งหมด {{ bill.items?.length ?? 0 }} รายการ</p>
              <p class="flex items-center text-xs text-neutral-400">
                <Icon
                  name="ph:alarm-bold"
                  class="mr-1 inline-block"
                />
                <span class="mr-1">สร้างเมื่อ</span>
                <FormatRelativeTime
                  :date="bill.created_at"
                />
              </p>
            </div>
          </div>
        </div>
        <Button
          v-if="billList.items.length > 1"
          :title="sessionId !== bill.created_by_id ? 'ออกจากบิล' : 'ลบบิล'"
          size="sm"
          :icon="sessionId !== bill.created_by_id ? 'ph:sign-out-bold' : 'ph:trash-bold'"
          color="neutral"
          variant="link"
          @click.stop.prevent="deleteBill(bill)"
        />
      </NuxtLink>
    </div>
    <div v-else>
      <div
        class="flex cursor-pointer items-center justify-center gap-2 rounded-lg border border-dashed border-gray-400 px-2 py-6 hover:text-primary hover:border-primary hover:bg-primary/10"
        @click="createModal.open()"
      >
        <Icon
          name="ph:plus-bold"
          class="text-xl"
        />
        <p>สร้างบิลใหม่</p>
      </div>
    </div>
  </Loader>
</template>

<script lang="ts" setup>
import { BillCreateModal } from '#components'
import type { BillState } from '~/types/bill-splitter'
import { getAvatarUrl } from '~/utils/avatar'

const props = defineProps<{
  forceReload?: boolean
}>()

defineEmits<{ select: [BillState] }>()

const billList = useBillList()
const dialog = useDialog()
const sessionId = useSessionId()
const remove = useBillRemove()
const noti = useNotification()
const overlay = useOverlay()
const createModal = overlay.create(BillCreateModal)

if (props.forceReload) {
  billList.setFetchLoading()

  onMounted(() => {
    billList.fetch()
  })
}

const getTotalAmount = (items: any[]) => {
  return items.reduce((total, item) => total + item.price, 0)
}

useWatchTrue(() => remove.status.value.isSuccess, () => {
  noti.success({
    title: 'ลบบิลสำเร็จ',
    description: 'บิลถูกลบเรียบร้อยแล้ว',
  })

  billList.fetch()
})

const deleteBill = (bill: BillState) => {
  if (sessionId.value === bill.created_by_id) {
    dialog.confirm({
      title: 'ยืนยันการลบบิล',
      description: 'คุณแน่ใจหรือไม่ว่าต้องการลบบิลนี้? การกระทำนี้ไม่สามารถย้อนกลับได้',
      confirmText: 'ลบ',
      cancelText: 'ยกเลิก',
    }).then(async () => {
      remove.remove(bill.id)
    })
  } else {
    dialog.confirm({
      title: 'ออกจากบิล',
      description: 'คุณแน่ใจหรือไม่ว่าต้องการออกจากบิลนี้? การกระทำนี้ไม่สามารถย้อนกลับได้',
      confirmText: 'ออกจากบิล',
      cancelText: 'ยกเลิก',
    }).then(async () => {
      await useBillRemoveMember(bill.id, sessionId.value)
      billList.fetch()
      noti.success({
        title: 'ออกจากบิลสำเร็จ',
        description: 'คุณได้ออกจากบิลนี้แล้ว',
      })
    })
  }
}
</script>
