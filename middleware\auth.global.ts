export default defineNuxtRouteMiddleware(async (to) => {
  if (to.query.auth_token) {
    const token = to.query.auth_token as string
    const authTokenCookie = useCookie('auth_token', {
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    })

    authTokenCookie.value = token

    // Remove token and sync_session from URL
    return navigateTo(to.path, {
      replace: true,
    })
  }

  // Handle auth error from URL
  if (to.query.auth_error) {
    console.error('Authentication error:', to.query.auth_error)

    // Show error to user (you might want to use a notification system)
    if (import.meta.client) {
      // alert(decodeURIComponent(to.query.auth_error as string))
    }

    // Remove error from URL
    return navigateTo(to.path, {
      replace: true,
    })
  }
})
