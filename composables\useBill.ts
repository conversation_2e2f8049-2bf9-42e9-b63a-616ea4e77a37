import { useAPIList, useAPIF<PERSON>, useAPIUpdate, useAPICreate } from './useSupabase'
import { useMappedSessionsCache } from './useMappedSessionsCache'
import type { BillState, FoodItem, Person, Transfer } from '~/types/bill-splitter'

export const useSessionId = () => {
  const sessionCookie = useCookie<string>('sessionId', {
    path: '/',
    maxAge: 60 * 60 * 24 * 365,
  })

  // Always return session UUID (never LINE user ID)
  return computed(() => {
    // Ensure session ID is always a UUID
    if (!sessionCookie.value) {
      sessionCookie.value = crypto.randomUUID()
    }

    return sessionCookie.value
  })
}

const select = 'id,created_at,created_by_id,name,items,people'

export const useBillList = defineStore('bills', () => {
  const sessionId = useSessionId()
  const auth = useAuth()

  // Create the base API list
  const baseList = useAPIList<BillState>({
    endpoint: 'bills',
    select: select,
    sort: {
      column: 'created_at',
      ascending: false,
    },
    or: `created_by_id.eq.${sessionId.value},member_ids.cs.{${sessionId.value}}`,
  })

  // Enhanced fetch function that includes mapped sessions for LINE users
  const enhancedFetch = async (fetchOptions = {} as IFetchOptions) => {
    let queryFilter = `created_by_id.eq.${sessionId.value},member_ids.cs.{${sessionId.value}}`

    // For LINE users, include mapped session UUIDs
    if (auth.user?.provider === 'line' && auth.user?.lineUserId) {
      try {
        // Use cached mapped sessions data
        const {
          getMappedSessionIds,
        } = useMappedSessionsCache()

        const mappedSessions = await getMappedSessionIds(auth.user.lineUserId)
        const allSessionIds = [sessionId.value, ...mappedSessions]

        // Build query to include all mapped session UUIDs
        const sessionQueries = allSessionIds.map((id) =>
          `created_by_id.eq.${id},member_ids.cs.{${id}}`,
        ).join(',')

        queryFilter = sessionQueries
      } catch (error) {
        console.error('Failed to get mapped sessions:', error)
        // Fall back to current session only
      }
    }

    // Use a custom query with the enhanced filter
    const supabase = useSupabaseClient()

    baseList.setFetchLoading()

    const query = supabase
      .from('bills')
      .select(select)
      .or(queryFilter)
      .order('created_at', {
        ascending: false,
      })

    const res = await query

    if (res.error) {
      baseList.status.value = ObjectHelper.toErrorStatus(baseList.status.value, res.error)
    } else {
      baseList.items.value = res.data as BillState[]
      baseList.status.value = ObjectHelper.toSuccessStatus(baseList.status.value)
    }

    baseList.status.value = ObjectHelper.toCompleteStatus(baseList.status.value)
  }

  return {
    ...baseList,
    fetch: enhancedFetch,
  }
})

export const useBillRemove = () => {
  return useAPIRemove({
    endpoint: 'bills',
  })
}

export const useBillRemoveMember = async (billId: string, sessionId: string) => {
  await useSupabaseClient().rpc('remove_member_id', {
    bill_id: billId,
    old_member_id: sessionId,
  })
}

export const useBillById = defineStore('bills.find', () => {
  const loader = useAPIFind<BillState>({
    endpoint: 'bills',
    select: select,
  })

  const addPerson = (() => {
    const personLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return personLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return personLoader.status.value
      }),
      update: (data: Person, isFillItems: boolean = false) => {
        const people = _cloneDeep(loader.item.value?.people || [])
        const items = _cloneDeep(loader.item.value?.items || [])

        people.push({
          name: data.name,
          promptPayId: data.promptPayId || null,
        })

        if (isFillItems) {
          items.forEach((item) => {
            item.sharing.push(true)
            item.sharingWeights.push(1)
          })
        } else {
          items.forEach((item) => {
            item.sharing.push(false)
            item.sharingWeights.push(0)
          })
        }

        return personLoader.update(loader.item.value!.id, {
          people: people,
          items: items,
        })
      },
    }
  })()

  const updatePerson = (() => {
    const personLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return personLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return personLoader.status.value
      }),
      update: (index: number, data: Person) => {
        if (index >= 0 && index < loader.item.value!.people.length) {
          const people = _cloneDeep(loader.item.value?.people || [])

          people[index] = {
            name: data.name,
            promptPayId: data.promptPayId || null,
          }

          return personLoader.update(loader.item.value!.id, {
            people: people,
          })
        }
      },
    }
  })()

  const removePerson = (() => {
    const personLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return personLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return personLoader.status.value
      }),
      update: (index: number) => {
        if (index >= 0 && index < loader.item.value!.people.length) {
          const people = _cloneDeep(loader.item.value?.people || [])
          const items = _cloneDeep(loader.item.value?.items || [])

          people.splice(index, 1)
          items.forEach((item) => {
            item.sharing.splice(index, 1)
            item.sharingWeights.splice(index, 1)
          })

          return personLoader.update(loader.item.value!.id, {
            people: people,
            items: items,
          })
        }

        return false
      },
    }
  })()

  const addItem = (() => {
    const itemLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return itemLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return itemLoader.status.value
      }),
      update: (item: FoodItem | FoodItem[]) => {
        const items = _cloneDeep(loader.item.value?.items || [])

        if (Array.isArray(item)) {
          item.forEach((newItem) => {
            items.push({
              ...newItem,
              sharing: newItem.sharing ? newItem.sharing : Array(loader.item.value!.people.length).fill(true),
              sharingWeights: newItem.sharingWeights
                ? newItem.sharingWeights
                : Array(loader.item.value!.people.length).fill(1),
            })
          })
        } else {
          items.push({
            ...item,
            sharing: item.sharing ? item.sharing : Array(loader.item.value!.people.length).fill(true),
            sharingWeights: item.sharingWeights ? item.sharingWeights : Array(loader.item.value!.people.length).fill(1),
          })
        }

        return itemLoader.update(loader.item.value!.id, {
          items: items,
        })
      },
    }
  })()

  const updateItem = (() => {
    const itemLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return itemLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return itemLoader.status.value
      }),
      update: (index: number, item: FoodItem) => {
        if (index >= 0 && index < loader.item.value!.items.length) {
          const items = _cloneDeep(loader.item.value?.items || [])

          items[index] = item

          return itemLoader.update(loader.item.value!.id, {
            items: items,
          })
        }
      },
    }
  })()

  const removeItem = (() => {
    const itemLoader = useAPIUpdate<BillState>({
      endpoint: 'bills',
      select: select,
    })

    return {
      item: computed<BillState | null>(() => {
        return itemLoader.item.value
      }),
      status: computed<IStatus>(() => {
        return itemLoader.status.value
      }),
      update: (index: number) => {
        if (index >= 0 && index < loader.item.value!.items.length) {
          const items = _cloneDeep(loader.item.value?.items || [])

          items.splice(index, 1)

          return itemLoader.update(loader.item.value!.id, {
            items: items,
          })
        }

        return false
      },
    }
  })()

  useWatchTrue(() => addPerson.status.value.isSuccess, () => {
    loader.item.value = addPerson.item.value
  })

  useWatchTrue(() => updatePerson.status.value.isSuccess, () => {
    loader.item.value = updatePerson.item.value
  })

  useWatchTrue(() => removePerson.status.value.isSuccess, () => {
    loader.item.value = removePerson.item.value
  })

  useWatchTrue(() => addItem.status.value.isSuccess, () => {
    loader.item.value = addItem.item.value
  })

  useWatchTrue(() => updateItem.status.value.isSuccess, () => {
    loader.item.value = updateItem.item.value
  })

  useWatchTrue(() => removeItem.status.value.isSuccess, () => {
    loader.item.value = removeItem.item.value
  })

  const personTotals = ref<number[]>([])
  const personPaid = ref<number[]>([])
  const transferCalculations = ref<Transfer[]>([])

  const grandTotal = computed(() => {
    return loader.item.value?.items.reduce((sum, item) => {
      return sum + (item.price || 0)
    }, 0) || 0
  })

  const calculateWeightedPrices = (item: FoodItem): number[] => {
    if (!loader.item.value) return []
    const pricesPerPerson = Array(loader.item.value.people.length).fill(0)
    let totalWeight = 0

    item.sharing.forEach((isSharing, idx) => {
      if (isSharing) totalWeight += item.sharingWeights[idx]
    })

    if (totalWeight === 0) return pricesPerPerson
    const pricePerWeight = item.price / totalWeight

    item.sharing.forEach((isSharing, idx) => {
      if (isSharing) {
        pricesPerPerson[idx] = pricePerWeight * item.sharingWeights[idx]
      }
    })

    return pricesPerPerson
  }

  const calculateTransfers = (): void => {
    if (!loader.item.value) return
    const balances = loader.item.value.people.map((_, index) => ({
      personIndex: index,
      balance: (personPaid.value[index] || 0) - (personTotals.value[index] || 0),
    }))

    const creditors = balances.filter((b) => b.balance > 0.01).sort((a, b) => b.balance - a.balance)
    const debtors = balances.filter((b) => b.balance < -0.01).sort((a, b) => a.balance - b.balance)

    transferCalculations.value = []
    let transferId = 0

    while (creditors.length > 0 && debtors.length > 0) {
      const creditor = creditors[0]
      const debtor = debtors[0]
      const transferAmount = Math.min(creditor.balance, Math.abs(debtor.balance))

      if (transferAmount > 0.01) {
        transferCalculations.value.push({
          id: transferId++,
          from: debtor.personIndex,
          to: creditor.personIndex,
          amount: Math.round(transferAmount * 100) / 100,
        })

        creditor.balance -= transferAmount
        debtor.balance += transferAmount
      }

      if (Math.abs(creditor.balance) < 0.01) creditors.shift()
      if (Math.abs(debtor.balance) < 0.01) debtors.shift()
    }
  }

  const calculateTotals = (): void => {
    if (!loader.item.value) return
    personTotals.value = Array(loader.item.value.people.length).fill(0)
    personPaid.value = Array(loader.item.value.people.length).fill(0)
    loader.item.value.items.forEach((item) => {
      const prices = calculateWeightedPrices(item)

      prices.forEach((price, idx) => {
        personTotals.value[idx] += price
      })

      if (item.paidBy !== null && item.paidBy !== undefined
        && item.paidBy >= 0
        && item.paidBy < ArrayHelper.toArray(loader.item.value?.people).length) {
        personPaid.value[item.paidBy] += item.price || 0
      }
    })

    calculateTransfers()
  }

  const shouldShowSummary = computed<boolean>(() => {
    return ArrayHelper.toArray(loader.item.value?.people).length > 0
      && ArrayHelper.toArray(loader.item.value?.items).length > 0
  })

  const shouldShowTransfers = computed(() => {
    return shouldShowSummary.value && transferCalculations.value.length > 0
  })

  useWatchTrue(() => loader.status.value.isSuccess, () => {
    calculateTotals()
  })

  watch(() => loader.item.value, () => {
    calculateTotals()
  }, {
    deep: true,
  })

  const createMember = async (sessionId: string) => {
    await useSupabaseClient().rpc('append_member_id', {
      bill_id: loader.item.value!.id,
      new_member_id: sessionId,
    })
  }

  const removeMember = async (sessionId: string) => {
    await useSupabaseClient().rpc('remove_member_id', {
      bill_id: loader.item.value!.id,
      old_member_id: sessionId,
    })
  }

  return {
    item: computed<BillState | null>(() => {
      return loader.item.value
    }),
    status: computed<IStatus>(() => {
      return loader.status.value
    }),
    find: loader.find,
    setFindItem: loader.setItem,
    setLoading: loader.setLoading,
    addPerson: addPerson,
    updatePerson: updatePerson,
    removePerson: removePerson,
    addItem: addItem,
    updateItem: updateItem,
    removeItem: removeItem,
    personTotals: personTotals,
    personPaid: personPaid,
    transferCalculations: transferCalculations,
    grandTotal: grandTotal,
    calculateTotals: calculateTotals,
    calculateWeightedPrices: calculateWeightedPrices,
    calculateTransfers: calculateTransfers,
    shouldShowSummary: shouldShowSummary,
    shouldShowTransfers: shouldShowTransfers,
    createMember,
    removeMember,
  }
})

export const useBillCreate = () => {
  const sessionId = useSessionId()

  const loader = useAPICreate<BillState>({
    endpoint: 'bills',
    select: select,
  })

  return {
    status: computed<IStatus>(() => {
      return loader.status.value
    }),
    create: (data: Partial<BillState>) => {
      return loader.create({
        ...data,
        created_by_id: sessionId.value,
        items: data.items || [],
        people: data.people || [],
      })
    },
    item: computed<BillState | null>(() => {
      return loader.item.value
    }),
  }
}
