# Project Brief

## Purpose
A modern web application for splitting bills among groups, designed to streamline the process of adding people, food items, and calculating individual shares. The app aims to provide a seamless, intuitive experience for managing shared expenses, with features for importing bills, assigning items, and summarizing payments.

## Core Requirements
- Allow users to add and manage people involved in a bill.
- Enable adding, editing, and assigning food items to people.
- Calculate and display each person's share of the total bill.
- Support importing bills (potentially via OCR or file upload).
- Provide clear summaries and instructions for payment transfers.
- Offer a responsive, user-friendly interface.

## Technical Goals
- Built with Nuxt (Vue 3) for a modern, performant SPA/SSR experience.
- Modular component-based architecture for maintainability.
- Use of composables for state management and sharing logic.
- Integration with Sentry for error tracking.
- Support for future extensibility (e.g., additional import/export formats, advanced splitting logic).

## Out of Scope
- User authentication and persistent user accounts (unless specified later).
- Payment processing integrations (focus is on calculation and summary, not actual transactions).

## Success Criteria
- Users can quickly and accurately split bills with minimal friction.
- The UI is intuitive and works well on both desktop and mobile devices.
- The app is robust, with error handling and clear feedback for users.
