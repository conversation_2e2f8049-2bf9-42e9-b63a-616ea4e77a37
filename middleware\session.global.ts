export default defineNuxtRouteMiddleware(async (to, from) => {
  // Check if user is authenticated first
  const auth = useAuth()
  const isAuthenticated = await auth.checkAuth()

  // If not authenticated, ensure we have a session ID for guest mode
  if (!isAuthenticated) {
    const cookieSessionId = useCookie('sessionId', {
      path: '/',
      maxAge: 60 * 60 * 24 * 365,
    })

    if (!cookieSessionId.value) {
      // Generate a proper UUID for created_by_id
      const newSessionId = crypto.randomUUID()

      cookieSessionId.value = newSessionId
    }
  }
})
