{"name": "paysync", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev -o", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --quiet", "lint:fix": "eslint --fix . --quiet", "prepare": "husky"}, "dependencies": {"@dicebear/collection": "^9.2.3", "@dicebear/core": "^9.2.3", "@finema/core": "^2.21.1", "@supabase/supabase-js": "^2.50.2", "@types/qrcode": "^1.5.5", "nuxt": "^3.17.5", "pako": "^2.1.0", "promptpay-qr": "^0.5.0", "qrcode": "^1.5.4", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.53", "@iconify-json/tdesign": "^1.2.8", "@nuxt/eslint-config": "^1.4.1", "@types/jsonwebtoken": "^9.0.10", "@types/pako": "^2.0.3", "eslint": "^9.26.0", "eslint-plugin-better-tailwindcss": "^3.4.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "^16.0.0"}, "lint-staged": {"*": "eslint --fix . --quiet"}, "overrides": {"@vercel/nft": "^0.27.4"}}