# PaySync Technical Stack

## Core Technologies

### Frontend Framework
- **Nuxt 3** (v3.17.5) - Vue 3 based meta-framework
- **Vue 3** (v3.5.14) - Progressive JavaScript framework
- **TypeScript** - Type-safe JavaScript development
- **SSR Disabled** - Client-side only application for simplicity

### Backend & Database
- **Supabase** - Backend-as-a-Service platform
  - PostgreSQL database with JSONB support
  - Real-time subscriptions via WebSocket
  - Authentication services
  - RPC functions for complex operations
- **Supabase Client** (@supabase/supabase-js v2.50.2)

### Authentication
- **LINE Login** - OAuth integration for Thai users
- **Guest Sessions** - UUID-based anonymous sessions
- **JWT Tokens** - Client-side token management
- **Session Mapping** - Database linking between LINE users and guest sessions

### UI Framework & Styling
- **Finema Core** (@finema/core v2.21.1) - Thai-optimized UI component library
- **Tailwind CSS** - Utility-first CSS framework (integrated via Finema)
- **Iconify** - Icon system with Lucide and TDesign icon sets
- **Dicebear** - Avatar generation system

### State Management
- **Pinia** - Vue 3 state management (via composables pattern)
- **VueUse** - Composition utilities (@vueuse/core)
- **Reactive APIs** - Vue 3 reactivity system

### Development Tools
- **ESLint** (v9.26.0) - Code linting with Nuxt config
- **Husky** (v9.1.7) - Git hooks for quality control
- **Lint-staged** (v16.0.0) - Pre-commit linting
- **Vite** - Build tool and development server

## Key Dependencies

### Payment & QR Code Generation
- **PromptPay QR** (v0.5.0) - Thai PromptPay QR code generation
- **QRCode** (v1.5.4) - General QR code generation
- **Types for QRCode** (@types/qrcode v1.5.5)

### Utilities
- **Pako** (v2.1.0) - Compression/decompression library
- **Vue Router** (v4.5.1) - Client-side routing

### Development Configuration
- **Bun** - Package manager and runtime
- **TypeScript Config** - Strict type checking enabled
- **EditorConfig** - Consistent coding style

## Architecture Patterns

### Component Architecture
- **Composables Pattern** - Business logic in reusable functions
- **Feature-Based Organization** - Components grouped by functionality
- **Modal/Overlay System** - Centralized UI state management
- **Atomic Design** - Reusable component hierarchy

### Data Flow
- **Reactive State** - Vue 3 reactivity for UI updates
- **Optimistic Updates** - Immediate UI feedback with server sync
- **Real-time Sync** - Supabase channels for collaborative editing
- **Error Handling** - Comprehensive error states and recovery

### Development Practices
- **TypeScript First** - Type safety throughout the application
- **Composition API** - Vue 3 composition patterns
- **Auto-imports** - Nuxt auto-import system for composables
- **Git Hooks** - Pre-commit linting and formatting

## Build & Deployment

### Build Configuration
- **Nuxt Generate** - Static site generation capability
- **Vite Optimization** - Dependency pre-bundling for performance
- **Source Maps** - Hidden client-side source maps
- **Tree Shaking** - Unused code elimination

### Environment Configuration
- **Runtime Config** - Environment variables for different deployments
- **PUBLIC Variables** - Client-side accessible configuration
- **Cookie Settings** - Secure session management
- **PWA Support** - Progressive Web App capabilities

### Performance Optimizations
- **Code Splitting** - Lazy loading of routes and components
- **Image Optimization** - Optimized asset delivery
- **Caching Strategies** - Browser and API response caching
- **Bundle Analysis** - Build size optimization

## Security Considerations

### Authentication Security
- **JWT Validation** - Token verification on client-side
- **Session Management** - Secure cookie handling
- **CSRF Protection** - Cross-site request forgery prevention
- **OAuth Integration** - Secure LINE login implementation

### Data Security
- **Input Validation** - Client and server-side validation
- **SQL Injection Prevention** - Parameterized queries via Supabase
- **XSS Protection** - Content sanitization
- **HTTPS Only** - Secure communication

## Development Setup

### Prerequisites
- Node.js (latest LTS)
- Bun package manager
- Supabase account and project

### Environment Variables
- `LINE_CHANNEL_SECRET` - LINE authentication secret
- `LINE_CHANNEL_ID` - LINE application ID
- `JWT_SECRET` - JWT signing secret
- `APP_BASE_URL` - Application base URL

### Development Commands
- `bun dev` - Start development server
- `bun build` - Build for production
- `bun generate` - Generate static site
- `bun lint` - Run ESLint
- `bun lint:fix` - Fix ESLint issues

## Integration Points

### External Services
- **LINE Platform** - Authentication and user profiles
- **Supabase** - Database, auth, and real-time features
- **PromptPay** - Thai payment system integration

### Browser APIs
- **Web Share API** - Native sharing capabilities
- **Clipboard API** - Copy/paste functionality
- **Local Storage** - Client-side data persistence
- **Service Workers** - PWA functionality

### Mobile Optimizations
- **Responsive Design** - Mobile-first approach
- **Touch Interactions** - Optimized for touch devices
- **Viewport Meta** - Mobile viewport configuration
- **App-like Experience** - PWA features for mobile users
