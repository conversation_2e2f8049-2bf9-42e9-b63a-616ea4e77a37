import { SITE } from './constants/site'

export default defineAppConfig({
  core: {
    color: '#14B8A6',
    site_name: SITE.TITLE,
    is_thai_year: true,
  },
  ui: {
    dialog: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
        base: '!rounded-2xl',
      },
    },
    toast: {
      slots: {
        progress: 'hidden',
      },
    },
    card: {
      slots: {
        root: '!bg-white',
        header: 'text-lg font-bold',
      },
    },
    skeleton: {
      base: 'bg-white',
    },
    switch: {
      slots: {
        base: 'cursor-pointer',
        label: 'cursor-pointer',
      },
    },
    breadcrumb: {
      variants: {
        active: {
          true: {
            link: 'text-black font-normal',
          },
        },
      },
    },
    tabs: {
      slots: {
        trigger: 'data-[state=active]:font-bold cursor-pointer',
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    drawer: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
      },
    },
    slideover: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
        title: 'text-xl font-bold',
        content: 'divide-none !rounded-t-2xl',
        body: 'sm:pt-0 pt-0 min-h-[250px]',
        footer: 'w-full',
      },
    },
    modal: {
      slots: {
        overlay: 'bg-black/50 backdrop-blur',
        title: 'text-xl font-bold',
        content: 'divide-none !rounded-2xl',
        body: 'sm:pt-0 pt-0',
        footer: 'w-full',
      },
    },
    pagination: {
      slots: {
        first: 'disabled:hidden',
        prev: 'disabled:hidden',
        next: 'disabled:hidden',
        last: 'disabled:hidden',
      },
    },
    commandPalette: {
      slots: {
        item: 'cursor-pointer',
      },
    },
    dropdownMenu: {
      slots: {
        item: 'cursor-pointer',
      },
    },
    table: {
      slots: {
        root: 'rounded-t-md rounded-b-md bg-white',
        captionContainer: 'hidden',
        paginationInfo: 'text-gray-600',
        paginationContainer: 'items-center flex-col lg:flex-row gap-4',
        thead: 'bg-[#475569]',
        th: 'text-white whitespace-nowrap',
        td: 'text-[#222222]',
      },
    },
    formField: {
      slots: {
        label: 'font-bold',
      },
    },
    input: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    inputNumber: {
      variants: {
        size: {
          xl: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    dateTime: {
      slots: {
        clearIcon: 'size-6 mr-3',
      },
    },
    selectMenu: {
      slots: {
        base: 'cursor-pointer w-full',
        item: 'cursor-pointer',
        clearIcon: 'size-6',
      },
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    textarea: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 disabled:bg-[#F5F5F5] disabled:text-[#737373] disabled:cursor-not-allowed',
          },
        },
      },
      defaultVariants: {
        size: 'xl',
      },
    },
    button: {
      variants: {
        size: {
          xl: {
            base: 'py-2.5 font-bold',
          },
        },
      },
      defaultVariants: {
        size: 'lg',
      },
    },
    stepper: {
      variants: {
        size: {
          xs: {
            trigger: 'size-8',
            icon: 'size-6',
            title: 'text-base font-bold',
            description: 'text-sm',
            wrapper: 'mt-1.5',
          },
        },
      },
      defaultVariants: {
        size: 'xs',
      },
    },
  },
},
)
