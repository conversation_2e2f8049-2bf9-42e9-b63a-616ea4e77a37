export default defineNuxtConfig({
  modules: ['@finema/core'],
  ssr: true, // Enable server-side rendering for better performance and SEO
  devtools: {
    enabled: true,
  },
  app: {
    head: {
      meta: [
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1, user-scalable=no',
        },
        {
          name: 'theme-color',
          content: '#14B8A6',
        },
        {
          name: 'application-name',
          content: 'PaySync',
        },
        {
          name: 'apple-mobile-web-app-title',
          content: 'PaySync',
        },
        {
          name: 'mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'apple-mobile-web-app-capable',
          content: 'yes',
        },
        {
          name: 'description',
          content: 'PaySync - แอปพลิเคชันแบ่งค่าอาหารและค่าใช้จ่ายอย่างยุติธรรม เหมาะสำหรับการทานอาหารกลุ่ม ปาร์ตี้ หรือการรวมตัว ช่วยให้การแบ่งเงินง่ายและโปร่งใส',
        },
        {
          name: 'keywords',
          content: 'PaySync, แบ่งค่าอาหาร, คำนวณค่าอาหาร, แอปแบ่งเงิน, split bill, แบ่งบิล, คำนวณค่าใช้จ่าย, ทานอาหารกลุ่ม, แบ่งเงินยุติธรรม, pay sync',
        },
        {
          property: 'og:type',
          content: 'website',
        },
        {
          property: 'og:site_name',
          content: 'PaySync',
        },
        {
          property: 'og:title',
          content: 'PaySync - แบ่งค่าอาหารอย่างยุติธรรม',
        },
        {
          property: 'og:description',
          content: 'แอปพลิเคชันแบ่งค่าอาหารและค่าใช้จ่ายอย่างยุติธรรม เหมาะสำหรับการทานอาหารกลุ่ม ปาร์ตี้ หรือการรวมตัว ช่วยให้การแบ่งเงินง่ายและโปร่งใส',
        },
        {
          name: 'twitter:title',
          content: 'PaySync - แบ่งค่าอาหารอย่างยุติธรรม',
        },
        {
          name: 'twitter:description',
          content: 'แอปพลิเคชันแบ่งค่าอาหารและค่าใช้จ่ายอย่างยุติธรรม เหมาะสำหรับการทานอาหารกลุ่ม ปาร์ตี้ หรือการรวมตัว',
        },
      ],
      link: [
        {
          rel: 'manifest',
          href: '/manifest.json',
        },
      ],
    },

  },
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    public: {
      lineChannelSecret: process.env.LINE_CHANNEL_SECRET || '11e67f450b00ab6a4fef2a6c0b4223af',
      jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret_key',
      lineChannelId: process.env.LINE_CHANNEL_ID || '2007681392',
      baseUrl: process.env.APP_BASE_URL || 'http://localhost:3000',
    },
  },
  sourcemap: {
    client: 'hidden',
  },
  compatibilityDate: '2025-05-15',
  vite: {
    optimizeDeps: {
      include: [
        '@vueuse/core',
        '@dicebear/collection',
        '@dicebear/core',
        'qrcode',
        'promptpay-qr',
        'pako',
        '@supabase/supabase-js',
      ],
    },
  },
  core: {},
})
