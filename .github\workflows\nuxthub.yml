name: Deploy to NuxtHub
on: push
jobs:
  deploy:
    name: "Deploy to NuxtHub"
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref == 'refs/heads/main' && 'production' || 'preview' }}
      url: ${{ steps.deploy.outputs.deployment-url }}
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest
      - name: Install dependencies
        run: bun install
      - name: Ensure NuxtHub module is installed
        run: bunx nuxthub@latest ensure
      - name: Build application
        run: bun run build
      - name: Deploy to NuxtHub
        uses: nuxt-hub/action@v1
        id: deploy
        with:
          project-key: paysync-mhalong-2d9o
