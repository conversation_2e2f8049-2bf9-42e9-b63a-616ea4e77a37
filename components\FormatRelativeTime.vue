<template>
  <span
    :title="fullDate"
  >
    {{ timeDistance }}
  </span>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { formatDistanceToNow, format } from 'date-fns'
import { th } from 'date-fns/locale'

const props = defineProps({
  date: {
    type: [Date, String, Number],
    required: true,
  },
  addSuffix: {
    type: Boolean,
    default: true,
  },
  includeSeconds: {
    type: Boolean,
    default: false,
  },
  autoUpdate: {
    type: Boolean,
    default: true,
  },
  updateInterval: {
    type: Number,
    default: 60000, // 1 minute
  },
})

const now = ref(new Date())
let intervalId: NodeJS.Timeout | null = null

const timeDistance = computed(() => {
  try {
    const dateObj = new Date(props.date)

    if (Number.isNaN(dateObj.getTime())) {
      return 'Invalid date'
    }

    return formatDistanceToNow(dateObj, {
      addSuffix: props.addSuffix,
      includeSeconds: props.includeSeconds,
      locale: th,
    })
  } catch (error) {
    console.error('Error formatting distance:', error)

    return 'Invalid date'
  }
})

const fullDate = computed(() => {
  try {
    const dateObj = new Date(props.date)

    if (Number.isNaN(dateObj.getTime())) {
      return 'Invalid date'
    }

    return format(dateObj, 'PPPPp', {
      locale: th,
    })
  } catch (error) {
    return 'Invalid date'
  }
})

const updateTime = () => {
  now.value = new Date()
}

onMounted(() => {
  if (props.autoUpdate) {
    intervalId = setInterval(updateTime, props.updateInterval)
  }
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>
