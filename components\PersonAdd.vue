<template>
  <div
    :class="[
      'border-primary flex cursor-pointer items-center justify-center',
      'rounded-full border border-dashed hover:bg-primary/10',
      {
        'size-10': size === 'md',
        'size-12': size === 'lg',
      },
    ]"
    @click="$emit('click', $event)"
  >
    <Icon
      name="ph:plus-bold"
      class="text-primary mx-auto size-6"
    />
  </div>
</template>

<script lang="ts" setup>
defineEmits(['click'])

withDefaults(defineProps<{
  size?: 'md' | 'lg'
}>(), {
  size: 'lg',
})
</script>
