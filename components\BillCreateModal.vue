<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    title="สร้างบิล"
  >
    <template #body>
      <Alert
        v-if="!auth.isAuthenticated"
        color="info"
        icon="ph:info-bold"
        variant="subtle"
        class="mb-4"
      >
        <template #description>
          การใช้งานแบบไม่เข้าสู่ระบบ
          อาจจะไม่สามารถบันทึกประวัติได้<br />
          หากต้องการรักษาประวัติการสร้างบิลของคุณไว้ กรุณา
          <span
            class="cursor-pointer font-bold"
            @click="onLogin()"
          >
            เข้าสู่ระบบ
          </span>
        </template>
      </Alert>
      <Form @submit="onSubmit">
        <FormFields :options="formFields" />
        <p class="mt-4 mb-1 text-sm font-bold">
          ผู้คน
        </p>
        <Log :data-items="[form.values]" />
        <div class="flex items-start justify-start gap-2">
          <PersonAvatar
            v-for="(person, index) in form.values?.people || []"
            :key="index"
            :person="person"
            action="edit"
            @click="handleEditPerson(index)"
          />
          <PersonAdd @click="handleAddPerson" />
        </div>
        <Submit />
      </Form>
    </template>
    <template #footer>
      <Button
        block
        class="mt-4"
        :loading="create.status.value.isLoading"
        @click="onSubmit"
      >
        สร้าง
      </Button>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { useMediaQuery } from '@vueuse/core'
import { LoginModal, Modal, Slideover } from '#components'
import PeopleFormModal from '~/components/PeopleFormModal.vue'

const emits = defineEmits<{ close: [boolean] }>()

const isDesktop = useMediaQuery('(min-width: 768px)')
const router = useRouter()
const auth = useAuth()
const create = useBillCreate()
const noti = useNotification()
const overlay = useOverlay()
const addModal = overlay.create(PeopleFormModal)
const editModal = overlay.create(PeopleFormModal)
const loginModal = overlay.create(LoginModal)
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.pipe(v.string(), v.nonEmpty()),
      people: v.pipe(v.array(
        v.object({
          name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
          promptPayId: v.nullish(v.pipe(v.string(), v.minLength(10)), null),
        }),
      )),
    }),
  ),
  initialValues: {
    name: '',
    people: [],
  },
})

const onLogin = () => {
  emits('close', false)
  loginModal.open()
}

const handleAddPerson = () => {
  addModal.open({
    onSubmit: (values) => {
      form.setFieldValue('people', [...form.values.people || [], values])

      addModal.close()
    },
  })
}

const handleEditPerson = (index: number) => {
  editModal.open({
    isEditing: true,
    values: form.values.people?.[index] || {},
    onSubmit: (values) => {
      form.setFieldValue(
        'people',
        form.values.people?.map((p, i) => (i === index ? values : p)),
      )

      editModal.close()
    },
    onRemove: async () => {
      form.setFieldValue(
        'people',
        form.values.people?.filter((_, i) => i !== index),
      )

      editModal.close()
    },
  })
}

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'ชื่อบิล',
      placeholder: 'ชื่อบิล',
      required: true,
      autoFocus: true,
    },
  },
])

useWatchTrue(() => create.status.value.isSuccess, async () => {
  emits('close', false)

  noti.success({
    title: 'บิลถูกสร้างเรียบร้อย',
    description: `บิล "${create.item.value?.name}" ถูกสร้างแล้ว`,
  })

  router.push(`/bills/${create.item.value?.id}`)
})

const onSubmit = form.handleSubmit((values) => {
  create.create(values)
})
</script>
