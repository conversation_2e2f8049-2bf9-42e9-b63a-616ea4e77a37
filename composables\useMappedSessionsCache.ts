import { useClientAuth } from './useClientAuth'

// Global cache for mapped sessions to avoid multiple API calls
const mappedSessionsCache = ref<{
  lineUserId: string
  sessionIds: string[]
  userData?: {
    displayName: string
    avatarUrl: string | null
  }
} | null>(null)

export const useMappedSessionsCache = () => {
  // Get mapped session data with caching
  const getMappedSessionData = async (lineUserId: string) => {
    // Return cached data if available for this user
    if (mappedSessionsCache.value?.lineUserId === lineUserId) {
      return {
        sessionIds: mappedSessionsCache.value.sessionIds,
        userData: mappedSessionsCache.value.userData,
      }
    }

    try {
      // Use client-side Supabase instead of server API
      const {
        getMappedSessions,
      } = useClientAuth()

      const response = await getMappedSessions(lineUserId)

      // Cache the response
      mappedSessionsCache.value = {
        lineUserId,
        sessionIds: response.sessionIds || [],
        userData: response.userData || undefined,
      }

      return {
        sessionIds: response.sessionIds || [],
        userData: response.userData || undefined,
      }
    } catch (error) {
      console.error('Failed to get mapped session data:', error)

      return {
        sessionIds: [],
        userData: undefined,
      }
    }
  }

  // Get only session IDs
  const getMappedSessionIds = async (lineUserId: string): Promise<string[]> => {
    const data = await getMappedSessionData(lineUserId)

    return data.sessionIds
  }

  // Clear cache
  const clearCache = () => {
    mappedSessionsCache.value = null
  }

  return {
    getMappedSessionData,
    getMappedSessionIds,
    clearCache,
  }
}
