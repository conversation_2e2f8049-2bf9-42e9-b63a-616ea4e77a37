<template>
  <div
    v-if="items.length > 0"
    class="mt-8 rounded-lg bg-gray-50 p-6 shadow"
  >
    <h3 class="mb-6 border-b pb-3 text-2xl font-semibold text-gray-800">
      สรุปรายการอาหาร
    </h3>
    <ul class="space-y-4">
      <li
        v-for="(item, itemIndex) in items"
        :key="itemIndex"
        class="rounded-md border border-gray-200 bg-white p-4 shadow-sm"
      >
        <div class="mb-2 flex items-center justify-between">
          <span class="text-lg font-medium text-indigo-600">{{ item.name }}</span>
          <span class="text-lg font-semibold text-gray-700">{{ NumberHelper.toCurrency(item.price) }}</span>
        </div>
        <div v-if="people.length > 0">
          <p class="mb-1 text-sm text-gray-600">
            แชร์โดย:
          </p>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="(person, personIndex) in people"
              :key="personIndex"
              class="rounded-full px-2 py-1 text-xs"
              :class="{
                'bg-green-100 text-green-700': item.sharing[personIndex],
                'bg-gray-100 text-gray-500': !item.sharing[personIndex],
              }"
            >
              {{ person.name }}
            </span>
            <span
              v-if="!item.sharing.some(s => s)"
              class="text-xs text-gray-400 italic"
            >
              ไม่มีใครแชร์รายการนี้
            </span>
          </div>
        </div>
        <div
          v-if="item.paidBy !== null && item.paidBy !== undefined && people[item.paidBy]"
          class="mt-3 border-t border-gray-100 pt-2"
        >
          <p class="text-sm text-gray-600">
            จ่ายโดย: <span class="font-medium text-blue-600">{{ people[item.paidBy]?.name }}</span>
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { FoodItem, Person } from '~/types/bill-splitter'

defineProps({
  items: {
    type: Array as PropType<FoodItem[]>,
    required: true,
  },
  people: {
    type: Array as PropType<Person[]>,
    required: true,
  },
})
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
