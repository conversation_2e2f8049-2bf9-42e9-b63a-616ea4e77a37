<template>
  <div class="min-h-screen pb-[100px] lg:pb-0">
    <AppHeader />
    <div class="mx-auto max-w-6xl space-y-4 overflow-hidden px-2 py-2 lg:px-4 lg:py-4">
      <div v-if="bill.status.isLoading">
        <div class="space-y-6 px-4">
          <div class="flex items-center gap-4">
            <Skeleton class="h-12 w-12 rounded-full" />
            <div class="grid gap-2">
              <Skeleton class="h-4 w-[250px]" />
              <Skeleton class="h-4 w-[200px]" />
            </div>
          </div>
          <div class="flex flex-col items-center justify-between gap-4 lg:flex-row">
            <Skeleton class="h-10 w-32 rounded" />
            <div class="grid grid-cols-2 gap-2 lg:flex lg:items-center lg:justify-start">
              <Skeleton class="h-10 w-24 rounded" />
              <Skeleton class="h-10 w-24 rounded" />
            </div>
          </div>
          <div class="grid gap-4 lg:grid-cols-6">
            <div class="space-y-4 lg:col-span-4">
              <Skeleton class="h-40 rounded" />
              <Skeleton class="h-40 rounded" />
            </div>
            <div class="space-y-4 lg:col-span-2">
              <Skeleton class="h-24 rounded" />
              <Skeleton class="h-24 rounded" />
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="!bill.status.isError">
        <div class="mb-4 flex flex-row items-start justify-between gap-4">
          <div>
            <p
              class="flex items-center justify-start text-2xl font-bold text-white"
            >
              {{ bill.item?.name }}
            </p>
            <p class="flex items-center justify-start text-xs text-neutral-100">
              <Icon
                name="ph:alarm-bold"
                class="mr-1 inline-block"
              />
              <span class="mr-1">สร้างเมื่อ</span>
              <FormatRelativeTime
                :date="bill.item!.created_at"
              />
            </p>
          </div>

          <div class="flex gap-2 lg:flex lg:items-center lg:justify-start">
            <Popover
              :ui="{ content: 'w-[300px]' }"
              :content="{
                align: 'end',
                side: 'bottom',
              }"
            >
              <Button
                icon="ph:share-network-bold"
                color="success"
                @click="handleShare"
              >
                แชร์ลิงก์
              </Button>
              <template #content>
                <div class="space-y-2 p-4">
                  <p class="text-sm font-bold">
                    แชร์ลิงก์
                  </p>
                  <div class="flex items-center gap-2">
                    <Input
                      :model-value="shareUrl"
                      readonly
                      size="lg"
                      tabindex="-1"
                    >
                      <template #trailing>
                        <Button
                          size="xs"
                          icon="ph:copy"
                          variant="outline"
                          class="bg-white"
                          label="คัดลอก"
                          @click="handleCopyLink"
                        />
                      </template>
                    </Input>
                  </div>
                </div>
              </template>
            </Popover>
          </div>
        </div>

        <div class="grid gap-4 lg:grid-cols-6">
          <div class="lg:col-span-4">
            <PeopleManager />
            <FoodItemsManager />
          </div>
          <div class="space-y-4 lg:col-span-2">
            <BillSummary />
            <TransferInstructions />
          </div>
        </div>
      </div>
      <Card
        v-else
        class="flex h-full min-h-[300px] items-center justify-center text-center"
      >
        <div>
          <p class="text-error">
            ไม่สามารถโหลดบิลได้
          </p>
          <div class="flex items-center justify-center gap-2">
            <p class="text-sm text-gray-500">
              อาจจะเกิดจากบิลถูกลบ หรือไม่มีการเชื่อมต่ออินเทอร์เน็ต
            </p>
          </div>
          <div class="mt-4 flex items-center justify-center gap-2">
            <Button
              icon="material-symbols:refresh"
              color="error"
              @click="bill.find(route.params.id as string)"
            >
              ลองอีกครั้ง
            </Button>
            <Button
              icon="material-symbols:home"
              color="primary"
              to="/"
            >
              กลับสู่หน้าหลัก
            </Button>
          </div>
        </div>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useShare, useClipboard } from '@vueuse/core'

const route = useRoute()
const request = useRequestURL()
const sessionId = useSessionId()
const bill = useBillById()
const supabase = useSupabaseClient()
const webShare = useShare()
const {
  copy,
} = useClipboard({
  source: `${request.origin}/${route.path}`,
})

const noti = useNotification()
let channel: any = null

const shareUrl = computed(() => {
  return `${request.origin}/${route.path}`
})

const handleShare = async () => {
  const shareData = {
    title: `บิล: ${bill.item?.name || 'ไม่ระบุชื่อ'}`,
    text: 'มาร่วมแชร์ค่าใช้จ่ายกันเถอะ!',
    url: shareUrl.value,
  }

  webShare.share(shareData)
}

const handleCopyLink = async () => {
  try {
    await copy(shareUrl.value)
    noti.success({
      title: 'คัดลอกลิงก์เรียบร้อย',
      description: 'ลิงก์ถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
    })
  } catch (error) {
    noti.error({
      title: 'ไม่สามารถคัดลอกลิงก์ได้',
      description: 'กรุณาลองใหม่อีกครั้ง',
    })
  }
}

bill.setLoading()
onMounted(() => {
  bill.find(route.params.id as string)
})

useWatchTrue(() => bill.status.isSuccess, () => {
  channel = supabase.channel(`realtime:bills:${route.params.id as string}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
        table: 'bills',
        filter: `id=eq.${route.params.id as string}`,
      },
      (payload) => {
        bill.setFindItem(payload.new as any)
      },
    )
    .subscribe()

  if (bill.item?.created_by_id !== sessionId.value) {
    bill.createMember(sessionId.value)
  }
})

onUnmounted(() => {
  if (channel) {
    supabase.removeChannel(channel)
  }
})
</script>
