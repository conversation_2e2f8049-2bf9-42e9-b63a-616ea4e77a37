<template>
  <Card
    class="mb-6"
    title="ผู้คน"
  >
    <div
      v-if="!ArrayHelper.isEmpty(bill.item?.people)"
      class="flex flex-wrap items-start justify-start gap-2"
    >
      <PersonAvatar
        v-for="(person, index) in bill.item?.people"
        :key="index"
        :person="person"
        action="edit"
        @click="handleEditPerson(index)"
      />
      <PersonAdd @click="handleAddPerson" />
    </div>
    <div
      v-else
      class="border-primary text-primary flex cursor-pointer items-center justify-center gap-2 rounded-lg border border-dashed px-2 py-4 hover:bg-primary/10"
      @click="handleAddPerson"
    >
      <Icon
        name="ph:plus-bold"
        class="size-6"
      />
      เพิ่มผู้คน
    </div>
  </Card>
</template>

<script setup lang="ts">
import PeopleFormModal from './PeopleFormModal.vue'

const bill = useBillById()
const overlay = useOverlay()
const noti = useNotification()
const addModal = overlay.create(PeopleFormModal)
const editModal = overlay.create(PeopleFormModal)

const handleAddPerson = () => {
  addModal.open({
    status: () => bill.addPerson.status,
    removeStatus: () => bill.removePerson.status,
    onSubmit: (values) => {
      bill.addPerson.update(values, values.is_sharing || false)
    },
  })
}

useWatchTrue(() => bill.addPerson.status.isSuccess, () => {
  noti.success({
    title: 'เพิ่มผู้คนสำเร็จ',
    description: 'ผู้คนถูกเพิ่มลงในบิลเรียบร้อยแล้ว',
  })

  addModal.close()
})

useWatchTrue(() => bill.updatePerson.status.isSuccess, () => {
  noti.success({
    title: 'แก้ไขผู้คนสำเร็จ',
    description: 'ผู้คนถูกแก้ไขเรียบร้อยแล้ว',
  })

  editModal.close()
})

useWatchTrue(() => bill.removePerson.status.isSuccess, () => {
  noti.success({
    title: 'ลบผู้คนสำเร็จ',
    description: 'ผู้คนถูกลบออกจากบิลเรียบร้อยแล้ว',
  })

  editModal.close()
})

const handleEditPerson = (index: number) => {
  editModal.open({
    isEditing: true,
    values: bill.item?.people[index],
    status: () => bill.updatePerson.status,
    removeStatus: () => bill.removePerson.status,
    onSubmit: (values) => {
      bill.updatePerson.update(index, values)
    },
    onRemove: async () => {
      bill.removePerson.update(index)
    },
  })
}
</script>
