export const utf8ToBase64 = (str: string) => {
  try {
    const encoder = new TextEncoder()
    const data = encoder.encode(str)

    return btoa(String.fromCharCode(...data))
  } catch (error) {
    console.error('Error encoding string to base64:', error)

    return str
  }
}

export const base64ToUtf8 = (base64: string): string => {
  try {
    // First decode the base64 string
    const binaryString = atob(base64)

    // Convert binary string to Uint8Array
    const bytes = new Uint8Array(binaryString.length)

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i)
    }

    // Use TextDecoder to properly handle UTF-8
    const decoder = new TextDecoder('utf-8')

    return decoder.decode(bytes)
  } catch (error) {
    console.error('Error decoding base64 to UTF-8:', error)

    return base64 // Return original string on error
  }
}

export const useLineAuth = () => {
  // Helper to create a simple JWT-like token for LINE users
  const createLineToken = (lineProfile: any) => {
    const payload = {
      sub: lineProfile.userId,
      name: lineProfile.displayName,
      picture: lineProfile.pictureUrl,
      provider: 'line',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 30), // 30 days
    }

    // Simple base64 encoding for client-side use (not secure but works for SPA)
    const header = btoa(JSON.stringify({
      alg: 'none',
      typ: 'JWT',
    }))

    const payloadStr = utf8ToBase64(JSON.stringify(payload))

    return `${header}.${payloadStr}.client-only`
  }

  // Initiate LINE Login
  const initiateLineLogin = () => {
    const config = useRuntimeConfig()
    const state = crypto.randomUUID()
    const nonce = crypto.randomUUID()

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: config.public.lineChannelId as string,
      redirect_uri: `${window.location.origin}/api/auth/line-callback`,
      state,
      scope: 'profile openid',
      nonce,
    })

    window.location.href = `https://access.line.me/oauth2/v2.1/authorize?${params.toString()}`
  }

  return {
    createLineToken,
    initiateLineLogin,
  }
}
