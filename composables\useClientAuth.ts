export const useClientAuth = () => {
  // Create session mapping (replaces /api/auth/sync-session)
  const createSessionMapping = async (
    sessionId: string,
    lineUserId: string,
    displayName: string,
    avatarUrl?: string | null,
  ) => {
    if (!sessionId || !lineUserId || !displayName) {
      throw new Error('Session ID, user ID, and display name are required')
    }

    const supabase = useSupabaseClient()

    // Check if mapping already exists
    const {
      data: existingMapping,
    } = await supabase
      .from('user_mappings')
      .select('*')
      .eq('line_user_id', lineUserId)
      .single()

    if (existingMapping) {
      // Mapping already exists, no need to create again
      return {
        success: true,
        message: 'User mapping already exists',
      }
    }

    // Create user mapping to link LINE user ID with session ID
    const {
      error: mappingError,
    } = await supabase
      .from('user_mappings')
      .insert({
        session_id: sessionId,
        line_user_id: lineUserId,
        display_name: displayName,
        avatar_url: avatarUrl,
        created_at: new Date().toISOString(),
      })

    if (mappingError) {
      console.error('Error creating user mapping:', mappingError)
      throw new Error('Failed to create user mapping')
    }

    return {
      success: true,
      message: 'User mapping created successfully',
    }
  }

  // Get mapped sessions for LINE user (replaces /api/auth/mapped-sessions)
  const getMappedSessions = async (lineUserId: string) => {
    if (!lineUserId) {
      throw new Error('LINE user ID is required')
    }

    const supabase = useSupabaseClient()

    // Get all session IDs and user data mapped to this LINE user
    const {
      data: mappings,
      error,
    } = await supabase
      .from('user_mappings')
      .select('session_id, display_name, avatar_url')
      .eq('line_user_id', lineUserId)

    if (error) {
      console.error('Error fetching user mappings:', error)
      throw new Error('Failed to fetch user mappings')
    }

    // Extract session IDs and user data from the mappings
    const sessionIds = mappings ? mappings.map((mapping) => mapping.session_id) : []
    const userData = mappings && mappings.length > 0
      ? {
        displayName: mappings[0].display_name,
        avatarUrl: mappings[0].avatar_url,
      }
      : null

    return {
      sessionIds,
      userData,
    }
  }

  return {
    createSessionMapping,
    getMappedSessions,
  }
}
