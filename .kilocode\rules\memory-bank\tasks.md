# PaySync Task Documentation

This file documents repetitive tasks and workflows that have been performed on the PaySync project. These serve as reference guides for future similar work.

## Task Examples

### Add New Component
**Last performed:** [Not yet documented]
**Files to modify:**
- `/components/[ComponentName].vue` - Create new component
- `/features/Main/index.vue` - Import and use component (if needed)
- `/types/bill-splitter.ts` - Add type definitions (if needed)

**Steps:**
1. Create component file with proper Vue 3 Composition API structure
2. Add TypeScript interfaces for props and emits
3. Implement component logic using appropriate composables
4. Add Thai language labels and text
5. Test component integration with existing features

**Important notes:**
- Follow existing component patterns and naming conventions
- Use Finema Core UI components for consistency
- Ensure mobile responsiveness
- Add proper error handling and loading states

### Update Authentication Flow
**Last performed:** [Not yet documented]
**Files to modify:**
- `/composables/useAuth.ts` - Core authentication logic
- `/composables/useLineAuth.ts` - LINE-specific authentication
- `/composables/useClientAuth.ts` - Client-side auth operations
- `/middleware/auth.global.ts` - Global auth middleware
- `/components/LoginModal.vue` - Login UI components

**Steps:**
1. Update authentication composable with new logic
2. Modify LINE authentication flow if needed
3. Update middleware to handle new auth patterns
4. Test both guest and LINE user authentication
5. Verify session synchronization works correctly

**Important notes:**
- Always test both authentication paths (guest and LINE)
- Ensure session mapping works for LINE users
- Verify token handling and expiration
- Test authentication persistence across page refreshes

### Add Real-time Feature
**Last performed:** [Not yet documented]
**Files to modify:**
- `/features/Main/index.vue` - Setup Supabase channel subscription
- `/composables/useBill.ts` - Add real-time handlers
- Relevant component files - Handle real-time updates

**Steps:**
1. Create Supabase channel subscription in main feature
2. Add event handlers for real-time updates
3. Implement optimistic updates for better UX
4. Add proper cleanup in onUnmounted
5. Test collaborative editing scenarios

**Important notes:**
- Always clean up subscriptions to prevent memory leaks
- Handle connection errors gracefully
- Implement optimistic updates for better user experience
- Test with multiple users simultaneously

### Database Schema Update
**Last performed:** [Not yet documented]
**Files to modify:**
- `/types/bill-splitter.ts` - Update TypeScript interfaces
- `/composables/useBill.ts` - Update CRUD operations
- `/composables/useSupabase.ts` - Add new API methods if needed
- Relevant components - Update to use new schema

**Steps:**
1. Update Supabase database schema
2. Update TypeScript interfaces to match new schema
3. Modify CRUD operations in composables
4. Update components to handle new data structure
5. Test all affected functionality

**Important notes:**
- Always update TypeScript types first
- Test backward compatibility where possible
- Update all related components and composables
- Verify data migration works correctly

*Note: This file will be updated as tasks are completed and documented.*
