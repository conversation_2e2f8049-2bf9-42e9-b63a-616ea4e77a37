<template>
  <div class="animate-pulse">
    <!-- Header Skeleton -->
    <div
      v-if="type === 'header'"
      class="flex items-center justify-between p-4"
    >
      <div class="h-8 w-32 rounded bg-white" />
      <div class="flex items-center space-x-3">
        <div class="h-8 w-8 rounded-full bg-white" />
        <div class="h-6 w-20 rounded bg-white" />
      </div>
    </div>

    <!-- Bill Card Skeleton -->
    <div
      v-else-if="type === 'bill-card'"
      class="space-y-3 rounded-lg border bg-white p-4"
    >
      <div class="flex items-start justify-between">
        <div class="space-y-2">
          <div class="h-5 w-32 rounded bg-white" />
          <div class="h-4 w-24 rounded bg-white" />
        </div>
        <div class="h-4 w-16 rounded bg-white" />
      </div>
      <div class="space-y-2">
        <div class="h-3 w-full rounded bg-white" />
        <div class="h-3 w-3/4 rounded bg-white" />
      </div>
    </div>

    <!-- Bill List Skeleton -->
    <div v-else-if="type === 'bill-list'" class="space-y-4">
      <SkeletonLoader
        v-for="i in count"
        :key="i"
        type="bill-card"
      />
    </div>

    <!-- Food Item Skeleton -->
    <div
      v-else-if="type === 'food-item'"
      class="rounded-lg border bg-white p-4"
    >
      <div class="mb-3 flex items-start justify-between">
        <div class="space-y-2">
          <div class="h-5 w-24 rounded bg-white" />
          <div class="h-4 w-16 rounded bg-white" />
        </div>
        <div class="h-6 w-20 rounded bg-white" />
      </div>
      <div class="space-y-2">
        <div class="h-3 w-full rounded bg-white" />
        <div class="h-3 w-2/3 rounded bg-white" />
      </div>
    </div>

    <!-- Person Card Skeleton -->
    <div
      v-else-if="type === 'person'"
      class="flex items-center space-x-3 rounded-lg border bg-white p-3"
    >
      <div class="h-10 w-10 rounded-full bg-white" />
      <div class="flex-1 space-y-2">
        <div class="h-4 w-20 rounded bg-white" />
        <div class="h-3 w-32 rounded bg-white" />
      </div>
      <div class="h-6 w-16 rounded bg-white" />
    </div>

    <!-- Button Skeleton -->
    <div v-else-if="type === 'button'" class="h-10 w-full rounded-lg bg-white" />

    <!-- Text Skeleton -->
    <div v-else-if="type === 'text'" class="space-y-2">
      <div class="h-4 w-full rounded bg-white" />
      <div class="h-4 w-3/4 rounded bg-white" />
      <div class="h-4 w-1/2 rounded bg-white" />
    </div>

    <!-- Login Modal Skeleton -->
    <div v-else-if="type === 'login-modal'" class="space-y-4 rounded-lg bg-white p-6">
      <div class="space-y-3 text-center">
        <div class="mx-auto h-6 w-48 rounded bg-white" />
        <div class="mx-auto h-4 w-64 rounded bg-white" />
      </div>
      <div class="space-y-3">
        <div class="h-12 w-full rounded-lg bg-white" />
        <div class="h-12 w-full rounded-lg bg-white" />
      </div>
    </div>

    <!-- Default Generic Skeleton -->
    <div v-else class="h-4 w-full rounded bg-white" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  type?: 'header' | 'bill-card' | 'bill-list' | 'food-item' | 'person' | 'button' | 'text' | 'login-modal' | 'generic'
  count?: number
}

withDefaults(defineProps<Props>(), {
  type: 'generic',
  count: 3,
})
</script>
