<template>
  <!-- Desktop Top Navbar -->
  <div class="hidden h-[70px] items-center bg-transparent text-white lg:flex">
    <div class="mx-auto flex w-full max-w-6xl justify-between gap-2 p-4">
      <div class="flex items-center gap-4">
        <NuxtLink
          to="/"
          class="text-xl font-bold"
        >
          PaySync
        </NuxtLink>
        <p class="text-sm text-white/80">
          แบ่งค่าอาหารอย่างยุติธรรม ไม่มีใครเสียเปรียบ
        </p>
      </div>
      <div class="flex items-center justify-end gap-2">
        <Button
          leading-icon="mingcute:bill-line"
          color="warning"
          @click="historyModal.open()"
        >
          บิลทั้งหมด
        </Button>
        <Button
          color="info"
          leading-icon="ph:plus-bold"
          @click="createModal.open()"
        >
          สร้างบิลใหม่
        </Button>

        <!-- User Menu Loading Skeleton -->
        <div
          v-if="auth.isLoading || auth.isAuthenticating"
          class="flex items-center space-x-2"
        >
          <div class="h-8 w-8 animate-pulse rounded-full bg-gray-200" />
          <div class="h-4 w-16 animate-pulse rounded bg-gray-200" />
        </div>

        <!-- User Menu -->
        <DropdownMenu
          v-else-if="auth.isAuthenticated"
          :items="userMenuItems"
          :ui="{
            content: 'w-48',
          }"
          :content="{
            align: 'end',
            side: 'top',
            sideOffset: 8,
          }"
        >
          <Button
            variant="link"
            size="sm"
            color="neutral"
            class="text-white hover:text-neutral-300"
            trailing-icon="i-lucide-chevron-down"
          >
            <img
              :src="auth.getAvatar()"
              :alt="auth.getDisplayName()"
              class="h-8 w-8 rounded-full"
            />
            <span class="text-sm font-medium">{{ auth.getDisplayName() }}</span>
          </Button>
        </DropdownMenu>

        <!-- Login Button -->
        <Button
          v-else
          icon="ph:sign-in"
          color="success"
          @click="loginModal.open()"
        >
          เข้าสู่ระบบ
        </Button>
      </div>
    </div>
  </div>

  <!-- Mobile Top Header (Logo only) -->
  <div class="flex h-[60px] items-center bg-transparent lg:hidden">
    <div class="max-w-6x mx-auto flex w-full items-center justify-start gap-3 px-2 lg:px-4">
      <NuxtLink
        to="/"
        class="text-3xl font-bold text-white"
      >
        PaySync
      </NuxtLink>
      <p class="hidden text-sm text-white/80 sm:block">
        แบ่งค่าอาหารอย่างยุติธรรม ไม่มีใครเสียเปรียบ
      </p>
    </div>
  </div>

  <!-- Mobile Bottom Navbar -->
  <div class="fixed right-0 bottom-4 left-0 z-50 mx-4 lg:hidden">
    <!-- Content -->
    <div class="flex items-center justify-center">
      <div class="flex h-[50px] items-center justify-end gap-2 rounded-full bg-black/50 py-2 pl-3 font-bold text-white shadow-2xl backdrop-blur-xs">
        <!-- Navigation Items -->
        <Button
          icon="mingcute:bill-line"
          label="บิลทั้งหมด"
          color="warning"
          class="text-md h-full rounded-full"
          @click="historyModal.open()"
        />
        <Button
          icon="ph:plus-bold"
          label="สร้างบิลใหม่"
          color="info"
          class="text-md h-full rounded-full"
          @click="createModal.open()"
        />
        <div class="flex h-full flex-col items-center justify-center">
          <DropdownMenu
            v-if="auth.isAuthenticated"
            :items="userMenuItems"
            :ui="{
              content: 'w-48',
            }"
            :content="{
              align: 'center',
              side: 'top',
              sideOffset: 16,
            }"
          >
            <img
              :src="auth.getAvatar()"
              :alt="auth.getDisplayName()"
              class="mr-1 size-[44px] cursor-pointer rounded-full"
            />
          </DropdownMenu>

          <!-- Login Button -->
          <Button
            v-else
            icon="ph:sign-in"
            label="เข้าสู่ระบบ"
            class="text-md mr-3 h-full rounded-full"
            @click="loginModal.open()"
          />
        </div>
      <!-- Profile/Login Button -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui'
import HistoryModal from './HistoryModal.vue'
import LoginModal from './LoginModal.vue'
import BillCreateModal from './BillCreateModal.vue'

const overlay = useOverlay()
const historyModal = overlay.create(HistoryModal)
const loginModal = overlay.create(LoginModal)
const createModal = overlay.create(BillCreateModal)
const auth = useAuth()

// User menu items
const userMenuItems = computed(() => {
  const items: DropdownMenuItem[][] = [
    [
      {
        label: auth.getDisplayName(),
        avatar: {
          src: auth.getAvatar(),
        },
        type: 'label',
      },
    ],
  ]

  // Add logout option only for LINE users
  if (!auth.isGuest) {
    items.push([
      {
        label: 'ออกจากระบบ',
        icon: 'i-lucide-log-out',
        onSelect: () => {
          auth.logout()
        },
      },
    ])
  } else {
    // Add guest info for guest users
    items.push([
      {
        label: 'ใช้งานแบบไม่ต้องล็อกอิน',
        type: 'label',
      },
    ])
  }

  return items
})
</script>

<style scoped>
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animation-delay-75 {
  animation-delay: 0.075s;
}
</style>
