<template>
  <Chip
    :text="`x${weight}`"
    size="xl"
    :show="weight === 1 ? false : true"
    color="warning"
    :ui="{
      base: 'px-1 py-2',
    }"
  >
    <div
      :class="['group flex flex-col items-center', {
        'cursor-pointer': action !== 'static',
      }]"
      :title="action === 'edit'
        ? `แก้ไข ${person.name}`
        : action === 'delete'
          ? `ลบ ${person.name}`
          : person.name"
      @click="$emit('click', $event)"
    >
      <div
        :class="['relative flex flex-col items-center justify-center rounded-full', {
          'size-8': size === 'sm',
          'size-10': size === 'md',
          'size-12': size === 'lg',
        }] "
      >
        <img
          :src="getAvatarUrl(person.name)"
          :class="['bg-primary/10 w-full rounded-full']"
        />
        <span
          v-if="action !== 'static'"
          class="absolute inset-0 flex items-center justify-center rounded-full bg-black/30 opacity-0 transition-opacity group-hover:opacity-100"
        >
          <Icon
            :name="action === 'edit' ? 'ph:pencil-bold' : 'ph:trash-bold'"
            class="size-5 text-white"
          />
        </span>
      </div>
      <p
        :class="['mt-1 max-w-14 truncate text-center text-xs font-semibold', {
          'group-hover:text-primary group-hover:font-bold': action !== 'static',
        }]"
      >
        {{ person.name }}
      </p>
    </div>
  </Chip>
</template>

<script lang="ts" setup>
import type { Person } from '~/types/bill-splitter'
import { getAvatarUrl } from '~/utils/avatar'

defineEmits(['click'])

withDefaults(defineProps<{
  person: Person | any
  action?: 'static' | 'edit' | 'delete'
  size?: | 'md' | 'lg' | 'sm'
  weight?: number
}>(), {
  action: 'static',
  size: 'lg',
  weight: 1,
})
</script>
