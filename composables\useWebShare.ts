export const useWebShare = () => {
  const isSupported = computed(() => {
    return typeof navigator !== 'undefined' && 'share' in navigator
  })

  const share = async (data: ShareData) => {
    if (!isSupported.value) {
      throw new Error('Web Share API is not supported')
    }

    try {
      await navigator.share(data)

      return true
    } catch (error) {
      // User cancelled the share or share failed
      console.error('Error sharing:', error)

      return false
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)

      return true
    } catch (error) {
      console.error('Error copying to clipboard:', error)

      return false
    }
  }

  return {
    isSupported,
    share,
    copyToClipboard,
  }
}
