# PaySync Architecture

## System Overview
PaySync is a client-side Nuxt 3 application with Supabase as the backend service. The architecture follows a modern SPA pattern with real-time collaboration features.

## Core Technologies
- **Frontend**: Nuxt 3 (Vue 3) with TypeScript
- **Backend**: Supabase (PostgreSQL + real-time subscriptions)
- **Authentication**: LINE Login + Guest sessions
- **UI Framework**: Finema Core (@finema/core)
- **State Management**: Pinia stores via composables
- **Real-time**: Supabase channels for collaborative editing

## Source Code Structure

### Pages & Routing
- [`pages/index.vue`](pages/index.vue) - Home page with bill list
- [`pages/bills/[id].vue`](pages/bills/[id].vue) - Individual bill view (delegates to Main feature)
- [`pages/line-callback.vue`](pages/line-callback.vue) - LINE authentication callback

### Core Features
- [`features/Main/index.vue`](features/Main/index.vue) - Main bill management interface with real-time collaboration

### Components Architecture
- [`components/AppHeader.vue`](components/AppHeader.vue) - App header with auth controls
- [`components/BillCreateModal.vue`](components/BillCreateModal.vue) - Bill creation modal
- [`components/BillImportModal.vue`](components/BillImportModal.vue) - OCR bill import modal
- [`components/BillSummary.vue`](components/BillSummary.vue) - Bill totals and calculations
- [`components/FoodItemsManager.vue`](components/FoodItemsManager.vue) - Food items management
- [`components/PeopleManager.vue`](components/PeopleManager.vue) - People/participants management
- [`components/TransferInstructions.vue`](components/TransferInstructions.vue) - Payment transfer instructions with PromptPay QR codes
- [`components/HistoryList.vue`](components/HistoryList.vue) - Bill history listing
- [`components/LoginModal.vue`](components/LoginModal.vue) - Authentication modal

### Composables (Business Logic)
- [`composables/useBill.ts`](composables/useBill.ts) - **Core bill management logic**
  - Bill CRUD operations
  - People management (add, update, remove)
  - Item management (add, update, remove)
  - Weighted price calculations
  - Transfer calculations and balance optimization
  - Session management for collaboration
- [`composables/useAuth.ts`](composables/useAuth.ts) - **Authentication system**
  - Guest and LINE user authentication
  - Session synchronization
  - JWT token handling
- [`composables/useSupabase.ts`](composables/useSupabase.ts) - **Database operations**
  - Generic CRUD operations (useAPIList, useAPICreate, useAPIUpdate, useAPIFind)
  - Supabase client configuration
- [`composables/useLineAuth.ts`](composables/useLineAuth.ts) - LINE-specific authentication
- [`composables/useUserMapping.ts`](composables/useUserMapping.ts) - Session mapping for LINE users
- [`composables/useMappedSessionsCache.ts`](composables/useMappedSessionsCache.ts) - Cached session mappings
- [`composables/useClientAuth.ts`](composables/useClientAuth.ts) - Client-side auth operations
- [`composables/useWebShare.ts`](composables/useWebShare.ts) - Web Share API integration

### Type Definitions
- [`types/bill-splitter.ts`](types/bill-splitter.ts) - **Core data models**
  - `BillState` - Complete bill with metadata
  - `FoodItem` - Food item with sharing weights and payment info
  - `Person` - Participant with PromptPay details
  - `Transfer` - Payment transfer instruction
  - `ShareableAppState` - Shareable bill data structure

### Middleware
- [`middleware/auth.global.ts`](middleware/auth.global.ts) - Global auth middleware for token handling
- [`middleware/session.global.ts`](middleware/session.global.ts) - Session management middleware

### Configuration
- [`nuxt.config.ts`](nuxt.config.ts) - Nuxt configuration with SSR disabled, PWA setup
- [`app.config.ts`](app.config.ts) - App configuration with UI customization
- [`constants/site.ts`](constants/site.ts) - Site constants and metadata

## Key Architectural Patterns

### State Management
- **Pinia Stores**: Implemented via composables pattern
- **Reactive State**: Vue 3 reactivity system
- **Computed Properties**: For derived state (totals, transfers)
- **Watchers**: For side effects and state synchronization

### Authentication Architecture
- **Dual Authentication**: Guest sessions + LINE login
- **Session Mapping**: LINE users can sync existing guest sessions
- **JWT Tokens**: Client-side token management
- **Session Persistence**: Cookie-based session storage

### Real-time Collaboration
- **Supabase Channels**: Real-time updates via WebSocket
- **Optimistic Updates**: Immediate UI updates with server sync
- **Conflict Resolution**: Last-write-wins for collaborative editing
- **Member Management**: Automatic member addition to shared bills

### Bill Splitting Algorithm
- **Weighted Sharing**: Customizable sharing weights per person per item
- **Payment Tracking**: Who paid for each item
- **Balance Calculation**: Net balance per person (paid - owed)
- **Transfer Optimization**: Minimal transfer algorithm to settle debts

### Data Flow
1. **User Actions** → Component events
2. **Component Events** → Composable methods
3. **Composable Methods** → Supabase API calls
4. **API Updates** → Real-time channel notifications
5. **Channel Updates** → Reactive state updates
6. **State Updates** → UI re-renders

## Critical Implementation Paths

### Bill Creation Flow
1. User creates bill via [`BillCreateModal`](components/BillCreateModal.vue)
2. [`useBillCreate`](composables/useBill.ts) handles creation
3. Bill stored in Supabase with session ID as creator
4. Navigation to bill detail page

### Collaborative Editing Flow
1. User joins bill via shared link
2. [`useBillById`](composables/useBill.ts) loads bill data
3. Real-time channel subscription established
4. User automatically added as member if not creator
5. All updates broadcast via Supabase channels

### Calculation Engine
1. **Item Assignment**: Items assigned to people with weights
2. **Weighted Calculation**: [`calculateWeightedPrices`](composables/useBill.ts) computes individual shares
3. **Payment Tracking**: Track who paid for each item
4. **Balance Calculation**: Net balance = paid - owed
5. **Transfer Optimization**: [`calculateTransfers`](composables/useBill.ts) minimizes number of transfers

### Authentication Flow
1. **Guest Mode**: Auto-generated UUID session
2. **LINE Login**: OAuth flow via LINE
3. **Session Sync**: LINE users can sync existing guest sessions
4. **Token Management**: JWT tokens stored in cookies
5. **Session Mapping**: Database mapping between LINE user ID and session UUIDs

## Component Relationships
- **Main Feature** orchestrates all sub-components
- **Manager Components** handle specific entity types (people, items)
- **Modal Components** handle user input forms
- **Summary Components** display calculated results
- **Composables** provide shared business logic across components

## Database Schema (Supabase)
- **bills** table: Core bill data with JSONB fields for people and items
- **user_sessions** table: Session mapping for LINE users
- **member_ids** field: Array of session UUIDs for collaboration
- **RPC functions**: `append_member_id`, `remove_member_id` for member management

## Performance Considerations
- **SSR Disabled**: Client-side only for simplicity
- **Lazy Loading**: Components and routes loaded on demand
- **Optimistic Updates**: Immediate UI feedback
- **Debounced Updates**: Prevent excessive API calls during editing
- **Cached Session Mappings**: Reduce database queries for LINE users
