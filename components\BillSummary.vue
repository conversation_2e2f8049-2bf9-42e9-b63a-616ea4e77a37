<template>
  <Card
    title="สรุปยอดที่ต้องจ่ายแต่ละคน"
  >
    <div
      v-if="bill.shouldShowSummary"
      class="flex flex-col space-y-2"
    >
      <div
        v-for="(person, index) in bill.item!.people"
        :key="index"
        class="bg-success-100 rounded-2xl p-3"
      >
        <div
          class="mb-0.5 flex items-center justify-between text-base font-medium"
          :title="person.name"
        >
          <PersonAvatar
            :person="person"
            size="sm"
          />
          <div class="text-right">
            <p class="text-success-600 space-x-2 font-bold">
              {{ NumberHelper.toCurrency(bill.personTotals[index]) }} ฿
            </p>
            <div class="mt-0.5 flex items-center justify-between gap-1 text-xs opacity-80">
              <span>จ่ายออกไป</span>
              <span>{{ NumberHelper.toCurrency(bill.personPaid[index]) }} ฿</span>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-success-100 mt-1 flex items-center justify-between rounded-full px-6 py-3">
        <div class="font-bold">
          ยอดรวมทั้งหมด
        </div>
        <div class="text-success-700 text-lg font-bold">
          {{ NumberHelper.toCurrency(bill.grandTotal) }} ฿
        </div>
      </div>
    </div>
    <div v-else>
      <div class="flex items-center justify-center gap-2 rounded-2xl border border-dashed border-gray-400 px-2 py-6">
        <p>ยังไม่มีรายการ</p>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
const bill = useBillById()
</script>
