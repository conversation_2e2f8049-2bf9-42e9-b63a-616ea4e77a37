export const useUserMapping = () => {
  const auth = useAuth()

  // Cache for mapped sessions data
  const mappedSessionsCache = ref<{
    lineUserId: string
    sessionIds: string[]
    userData?: {
      displayName: string
      avatarUrl: string | null
    }
  } | null>(null)

  // Get mapped session data for the current LINE user (cached)
  const getMappedSessionData = async () => {
    if (!auth.user?.lineUserId) {
      return {
        sessionIds: [],
        userData: undefined,
      }
    }

    // Return cached data if available for this user
    if (mappedSessionsCache.value?.lineUserId === auth.user.lineUserId) {
      return {
        sessionIds: mappedSessionsCache.value.sessionIds,
        userData: mappedSessionsCache.value.userData,
      }
    }

    try {
      const response = await $fetch<{
        sessionIds: string[]
        userData?: {
          displayName: string
          avatarUrl: string | null
        }
      }>('/api/auth/mapped-sessions', {
        method: 'POST',
        body: {
          lineUserId: auth.user.lineUserId,
        },
      })

      // Cache the response
      mappedSessionsCache.value = {
        lineUserId: auth.user.lineUserId,
        sessionIds: response.sessionIds || [],
        userData: response.userData,
      }

      return {
        sessionIds: response.sessionIds || [],
        userData: response.userData,
      }
    } catch (error) {
      console.error('Failed to get mapped session data:', error)

      return {
        sessionIds: [],
        userData: undefined,
      }
    }
  }

  // Get only session IDs (for backward compatibility)
  const getMappedSessionIds = async (): Promise<string[]> => {
    const data = await getMappedSessionData()

    return data.sessionIds
  }

  // Check if a session ID belongs to the current user (direct or mapped)
  const isUserSession = async (sessionId: string): Promise<boolean> => {
    const currentSessionId = useCookie('sessionId').value

    // Direct match
    if (sessionId === currentSessionId) {
      return true
    }

    // Check mapped sessions for LINE users
    if (auth.user?.lineUserId) {
      const mappedSessions = await getMappedSessionIds()

      return mappedSessions.includes(sessionId)
    }

    return false
  }

  // Clear cache when user changes
  const clearCache = () => {
    mappedSessionsCache.value = null
  }

  return {
    getMappedSessionData,
    getMappedSessionIds,
    isUserSession,
    clearCache,
  }
}
