<template>
  <component
    :is="isDesktop ? Modal : Slideover"
    side="bottom"
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขผู้คน' : 'เพิ่มผู้คน'"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <Submit />
      </form>
    </template>
    <template #footer>
      <div class="flex flex-1 flex-col justify-end gap-3">
        <Button
          block
          :disabled="!form.meta.value.dirty"
          :loading="props.status?.().isLoading"
          @click="onSubmit"
        >
          {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
        </Button>
        <Button
          v-if="isEditing"
          block
          icon="ph:trash-bold"
          color="error"
          variant="outline"
          :loading="props.removeStatus?.().isLoading"
          @click="onRemove"
        >
          ลบ
        </Button>
      </div>
    </template>
  </component>
</template>

<script lang="ts" setup>
import { useMediaQuery } from '@vueuse/core'
import { Modal, Slideover } from '#components'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: any
  onSubmit: (values: any) => void
  onRemove?: () => void
  status?: () => IStatus
  removeStatus?: () => IStatus
}>()

const bill = useBillById()
const isDesktop = useMediaQuery('(min-width: 768px)')
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      name: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      promptPayId: v.nullish(v.pipe(v.string(), v.minLength(10)), null),
      is_sharing: v.optional(v.boolean(), false),
    }),
  ),
  initialValues: props.values,
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'ชื่อ',
      required: true,
      autoFocus: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'promptPayId',
      label: 'รหัสพร้อมเพย์',
      placeholder: 'เช่น 08x-xxx-xxxx หรือ 1-xxxx-xxxxx-xx-x',
      mask: '#',
      maskTokens: {
        '#': {
          pattern: /[0-9\n]/,
          repeated: true,
        },
      },
    },
  },
  {
    type: INPUT_TYPES.TOGGLE,
    isHide: bill.item?.items?.length === 0 || props.values,
    props: {
      name: 'is_sharing',
      label: 'แชร์ค่าใช้จ่ายในรายการอาหารปัจจุบัน',
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values)
})
</script>
